// @vitest-environment jsdom
import {renderHook} from '@testing-library/react';
import * as Hooks from '../redux/hooks';
import useConfig from './useConfig';

vi.mock('../redux/hooks', () => ({
  useAppSelector: vi.fn(),
}));
const useAppSelector = Hooks.useAppSelector as jest.Mock;

describe('useConfig', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('returns config with correct type conversions', () => {
    useAppSelector.mockReturnValue({
      authApiBaseUrl: 'https://api.example.com',
      enableSessionTimeout: 'true',
      expiryTimeInMinute: '30',
      promptTimeBeforeIdleInMinute: '5',
    });
    const {result} = renderHook(() => useConfig());
    expect(result.current.config).toMatchObject({
      authApiBaseUrl: 'https://api.example.com',
      enableSessionTimeout: true,
      expiryTimeInMinute: 30,
      promptTimeBeforeIdleInMinute: 5,
    });
  });

  it('returns config with default expiry and prompt time if missing', () => {
    useAppSelector.mockReturnValue({
      authApiBaseUrl: 'https://api.example.com',
      enableSessionTimeout: 'false',
    });
    const {result} = renderHook(() => useConfig());
    expect(result.current.config).toMatchObject({
      authApiBaseUrl: 'https://api.example.com',
      enableSessionTimeout: false,
      expiryTimeInMinute: 15,
      promptTimeBeforeIdleInMinute: 1,
    });
  });

  it('handles undefined configData', () => {
    useAppSelector.mockReturnValue(undefined);
    const {result} = renderHook(() => useConfig());
    expect(result.current.config).toEqual({});
  });

  it('passes through extra fields', () => {
    useAppSelector.mockReturnValue({
      authApiBaseUrl: 'url',
      enableSessionTimeout: 'true',
      expiryTimeInMinute: '10',
      promptTimeBeforeIdleInMinute: '2',
      tenantApiBaseUrl: 'tenant-url',
      storageSessionTimeKey: 'key',
      extra: 'should-pass',
    });
    const {result} = renderHook(() => useConfig());
    expect(result.current.config.tenantApiBaseUrl).toBe('tenant-url');
    expect(result.current.config.storageSessionTimeKey).toBe('key');
    expect((result.current.config as any).extra).toBe('should-pass');
  });
});
