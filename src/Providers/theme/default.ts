import {PaletteOptions} from '@mui/material';
import {Integers} from 'Helpers/integers';

type ShrinkPaletteOption = Omit<PaletteOptions, 'mode'>;
export const paletteConfig: {
  light: ShrinkPaletteOption;
  dark: ShrinkPaletteOption;
} = {
  light: {
    background: {
      default: '#ffffff',
    },
    primary: {
      main: '#852352',
      50: '#F7ECF1',
      100: '#E9C1CF',
      150: '#E9C1CF',
      200: '#CBA0AF',
      300: '#AE8091',
      400: '#916173',
      500: '#754457',
      600: '#59273C',
      700: '#3E0923',
      800: '#21000D',
      900: '#030000',
    },
    secondary: {
      main: '#173478',
      25: '#F3F3FA',
      50: '#F0F0FB',
      100: '#C0CEEA',
      200: '#9FAECD',
      300: '#7F8FB0',
      400: '#607294',
      500: '#435579',
      600: '#28395e',
      650: '#0E2762',
      700: '#0E1F45',
      800: '#00052C',
      900: '#00000A',
    },
    text: {
      secondary: '#6B6B6B',
      primary: '#2C2C2C',
    },
    divider: '#e0e0e0',
    table: {
      header: '#EFEFF2',
      accordianBg: '#F7F7F7',
    },
    body: {
      dark: '#0A0A0A', // Light gray for body background
      50: '#F1F1F1',
      100: '#DBDBDB',
      200: '#BABABA',
      300: '#9D9D9D',
      350: '#999999',
      400: '#838383',
      500: '#6B6B6B',
      600: '#545454',
      700: '#3F3F3F',
      800: '#2C2C2C',
      900: '#1A1A1A',
    },

    white: {
      main: '#ffffff',
      100: '#F9F9F9',
      200: '#F0F0FB',
      300: '#DBDBDB',
    },
    black: {
      main: '#000000',
    },
    sideNav: {
      active: '#173478',
      hover: '#F7F7F7',
      linkTextActive: '#435579',
    },
    alert: {
      success: {
        main: '#1AC371',
        bg: '#E2FFF1',
        onBg: '#0D653B',
        border: '#80B29A',
      },
      error: {
        main: '#FF1818',
        bg: '#FFE4E5',
        onBg: '#A91417',
        border: '#E8ABAC',
      },
      warning: {
        main: '#E7BF1B',
        bg: '#FFF9E0',
        onBg: '#736116',
        border: '#E9E0BD',
      },
      info: {
        main: '#4278F8',
        bg: '#F0F0FB',
        onBg: '#17377F',
        border: '#CECEE8',
      },
    },
    tenantStatus: {
      failed: {
        bg: '#FFE7DC',
        indicator: '#FF7E42',
        onBg: '#79310F',
      },
      provisioning: {
        bg: '#F3ECE4',
        indicator: '#9D815F',
        onBg: '#483014',
      },
      suspended: {
        bg: '#FDD8C7',
        onBg: '#87340E',
        indicator: '#E35615',
      },
      suspendedOverdue: {
        bg: '#FCEBA5',
        onBg: '#6D5A0F',
        indicator: '#9C800A',
      },
      onboarding: {
        bg: '#E6F0FA',
        onBg: '#0D3A5B',
        indicator: '#2A82D1',
      },
      migrating: {
        bg: '#E2E9FF',
        onBg: '#13255F',
        indicator: '#2041AA',
      },
      migrationFailed: {
        bg: '#FFC2C2',
        onBg: '#520D0D',
        indicator: '#D40000',
      },
      pendingProvisioningExpired: {
        bg: '#F7E3BE',
        onBg: '#7A5719',
        indicator: '#FEA300',
      },
      deprovisioning: {
        bg: '#FBD1F1',
        onBg: '#6E1358',
        indicator: '#BC2598',
      },
      deprovisionFailed: {
        bg: '#ECDDE8',
        onBg: '#5C1248',
        indicator: '#76115E',
      },
      suspending: {
        bg: '#E9E7E4',
        onBg: '#393022',
        indicator: '#5F5545',
      },
      suspensionFailed: {
        bg: '#FFC195',
        onBg: '#632306',
        indicator: '#AD4210',
      },
      reactivating: {
        bg: '#F0F3D3',
        onBg: '#5A6117',
        indicator: '#A8B713',
      },
      reactivationFailed: {
        bg: '#F5C7C7',
        onBg: '#721A1A',
        indicator: '#980B0B',
      },
      pendingReactivation: {
        bg: '#DDE0BE',
        onBg: '#3A3E10',
        indicator: '#5D6416',
      },
    },
    plan: {
      normal: {
        1: '#C2D4FF',
        2: '#FFF2F8',
      },
      selected: {
        1: '#AAC4FF',
        2: '#FFE8F3',
      },
    },
    extra: {
      disabledInputBg: '#E9E9F1',
    },
  },
  dark: {
    text: {
      primary: '#f8fafc',
      secondary: '#cbd5e1',
    },
    divider: '#374151',
    border: {
      main: '#ffffff1f',
    },
  },
};

const MUIPickerDefaultStyle = {
  backgroundColor: 'secondary.main', // selected day color
  color: 'white.main', // selected day text color
  fontWeight: 600,
};

const thumbColor = paletteConfig?.light?.body?.[Integers.ThreeHundred];
const scrollTrackingColor = paletteConfig?.light?.body?.[Integers.OneHundred];

export const commonConfig = {
  typography: {
    fontFamily: ['Lato', 'Inter', 'Roboto', 'Arial', 'sans-serif'].join(','),
    fontSize: 16,
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      lineHeight: 1.3,
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      lineHeight: 1.4,
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    h6: {
      fontSize: '1.125rem',
      fontWeight: 500,
      lineHeight: 1.4,
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.5,
    },
    button: {
      textTransform: 'none' as const,
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: '0.5rem', // 8px converted to rem (8/16 = 0.5)
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        '*::-webkit-scrollbar': {
          height: '0.5rem', // 8px converted to rem
          width: '0.5rem',
        },
        '*::-webkit-scrollbar-track': {
          background: scrollTrackingColor,
          borderRadius: '6.25rem',
        },
        '*::-webkit-scrollbar-thumb': {
          backgroundColor: thumbColor,
          borderRadius: '6.25rem',
          opacity: '0.5',
        },
        '*::-webkit-scrollbar-thumb:horizontal': {
          width: '0.5rem', // 8px converted to rem
          height: '18.75rem', // 300px converted to rem
          backgroundColor: thumbColor,
          backgroundClip: 'padding-box',
          borderRight: '1rem white solid', // 16px converted to rem
        },
      },
    },
    MuiPickersDay: {
      styleOverrides: {
        root: {
          color: 'body.dark',
          '&.Mui-selected:focus': MUIPickerDefaultStyle,
          '&.Mui-selected': MUIPickerDefaultStyle,
          '&.Mui-selected:hover': {
            fontWeight: 600,
            backgroundColor: 'secondary.500',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        sizeLarge: {
          minHeight: '3.125rem', // 50px converted to rem
          p: 0,
        },
        root: {
          borderRadius: '0.5rem', // 8px converted to rem
          padding: '0.75rem 1.5rem', // 12px 24px converted to rem
          fontSize: '0.9rem',
          fontWeight: 500,
          textTransform: 'none' as const,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 0.25rem 0.5rem #00000020', // rgba converted to hex with opacity
          },
        },
        contained: {
          '&:hover': {
            boxShadow: '0 0.25rem 0.75rem #00000026', // rgba converted to hex with opacity
          },
        },
      },
    },
    MuiOutlinedInput: {
      styleOverrides: {
        root: {
          borderRadius: '0.5rem', // 8px converted to rem
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: '#d1d5db',
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            borderWidth: '0.125rem', // 2px converted to rem
          },
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          fontSize: '0.875rem',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '0.75rem', // 12px converted to rem
          boxShadow: '0 0.0625rem 0.1875rem #0000001a', // rgba converted to hex with opacity
        },
      },
    },
  },
};
