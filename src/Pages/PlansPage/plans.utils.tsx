import {CellContext} from '@tanstack/react-table';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import {Integers} from 'Helpers/integers';
import {ISoftwareLevel} from 'redux/app/types/plan.type';
import * as Yup from 'yup';
import {ActionButtons} from './PlanPage';
import PlanStatusChip from './PlanStatus';

export const DEFAULT_LIMIT = 5;
export const DEFAULT_OFFSET = 0;

export enum PlanStatus {
  ACTIVE,
  INACTIVE,
  TRIAL,
}
export const baseFilterForPlanDetail = {
  include: [
    {relation: 'billingCycle'},
    {
      relation: 'planHistories',
      scope: {
        order: 'createdOn DESC',
      },
    },
    {relation: 'currency'},
    {relation: 'planSize'},
    {relation: 'configureDevice'},
    {relation: 'softwareLevel'},
  ],
};

export enum PlanTierType {
  PREMIUM = 'premium',
  STANDARD = 'standard',
}

export const getPlanTierType = (type?: string): PlanTierType | undefined => {
  switch (type?.toLowerCase()) {
    case 'premium':
      return PlanTierType.PREMIUM;
    case 'standard':
      return PlanTierType.STANDARD;
    default:
      return undefined;
  }
};
export interface DeviceConfigForm {
  min: number | undefined;
  max: number | undefined;
  isEdited?: boolean; // Track if this row has been modified
  id?: string; // Optional ID for existing records
}

export interface DeviceConfigFormikValues {
  items: DeviceConfigForm[];
  deletedItems?: DeviceConfigForm[]; // Track deleted items
}

/**
 * Utility function to get only the edited rows from form values
 * @param formValues - The form values containing all items
 * @returns Array of edited items with their original indices
 */
export const getEditedRows = (formValues: DeviceConfigFormikValues) =>
  formValues.items.map((item, index) => ({...item, originalIndex: index})).filter(item => item.isEdited);

/**
 * Utility function to get count of edited rows
 * @param formValues - The form values containing all items
 * @returns Number of edited rows
 */
export const getEditedRowsCount = (formValues: DeviceConfigFormikValues) =>
  formValues.items.filter(item => item.isEdited).length;

/**
 * Utility function to get deleted rows
 * @param formValues - The form values containing all items
 * @returns Array of deleted items
 */
export const getDeletedRows = (formValues: DeviceConfigFormikValues) => formValues.deletedItems || [];

/**
 * Utility function to get count of deleted rows
 * @param formValues - The form values containing all items
 * @returns Number of deleted rows
 */
export const getDeletedRowsCount = (formValues: DeviceConfigFormikValues) => (formValues.deletedItems || []).length;

/**
 * Get the validation schema for the Device Config form
 * Implements complex validation rules:
 * 1. Overall form min=1, max=64
 * 2. Row-level: max > min for each row
 * 3. Sequential: next row min > previous row max
 * @returns Yup validation schema
 */
export const getDeviceConfigFormValidationSchema = () =>
  Yup.object({
    items: Yup.array().of(
      Yup.object().shape({
        min: Yup.number()
          .min(Integers.One, 'Min value must be at least 1')
          .max(Integers.SixtyFour, 'Min value must be at most 64')
          .required('Min value is required')
          .typeError('Min value must be a number'),
        max: Yup.number()
          .min(Integers.One, 'Max value must be at least 1')
          .max(Integers.SixtyFour, 'Max value must be at most 64')
          .required('Max value is required')
          .typeError('Max value must be a number')
          .test('max-greater-than-min', 'Max value must be greater than Min value', function (value) {
            const {min} = this.parent;
            return !min || !value || Number(value) > Number(min);
          }),
      }),
    ),
  });

export const groupedFeatureOptions = [
  {
    label: 'Standard',
    value: PlanTierType.STANDARD,
    features: ['Pooled compute', 'Silo storage'],
  },
  {
    label: 'Premium',
    value: PlanTierType.PREMIUM,
    features: ['Silo compute', 'Silo storage'],
  },
];

export const getCombinedPlanTierName = (type: PlanTierType): string | undefined => {
  const tier = groupedFeatureOptions.find(t => t.value === type);
  return tier ? `${tier.label} (${tier.features.join(', ')})` : undefined;
};

interface BillingCycle {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string | null;
  createdBy: string;
  modifiedBy: string | null;
  id: string;
  cycleName: string;
  duration: number;
  durationUnit: string;
  description: string;
}

interface ICurrency {
  id: string;
  currencyCode: string;
  currencyName: string;
  symbol: string;
  country: string;
}

interface ConfigureDevice {
  deleted: boolean;
  deletedOn: string | null;
  deletedBy: string | null;
  createdOn: string;
  modifiedOn: string | null;
  createdBy: string;
  modifiedBy: string | null;
  id: string;
  min: string;
  max: string;
}

interface PlanTableRow {
  name: string;
  price: number;
  status: PlanStatus;
  configureDevice: ConfigureDevice;
  currency: ICurrency;
  billingCycle: BillingCycle;
  tier: string;
  softwareLevel: ISoftwareLevel;
}

interface PlanTableColumn {
  header: string;
  accessorKey?: keyof PlanTableRow;
  id?: string;
  cell?: (context: CellContext<PlanTableRow, unknown>) => React.ReactNode;
}

const columnNameMap: Record<string, string> = {
  planName: 'name',
  status: 'status',
  configureDevice: 'configureDevice',
  currency: 'currency',
  billingCycle: 'billingCycle',
  tier: 'tier',
};

/**
 * Returns the corresponding backend column name for a given frontend column name.
 * If the column name does not exist in the mapping, returns the original column name.
 *
 * @param columnName - The frontend column name to map.
 * @returns The backend column name if found in the mapping; otherwise, the original column name.
 */
export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

/**
 * Capitalizes the first character of the given string and converts the rest to lowercase.
 *
 * @param str - The string to capitalize.
 * @returns The capitalized string.
 */
export const capitalize = (str: string): string => str?.charAt(0).toUpperCase() + str?.slice(1).toLowerCase();

/**
 * Defines the column configuration for the Plans table.
 *
 * Each column object specifies how the column should be rendered, including:
 * - `header`: The display name of the column.
 * - `accessorKey`: The key to access the corresponding value from the row data.
 * - `id`: A unique identifier for the column.
 * - `cell`: (Optional) A custom cell renderer function for displaying complex or formatted data.
 *
 * Columns include:
 * - Plan name
 * - Status (with custom status chip rendering)
 * - Number of devices (range or placeholder)
 * - Subscription Tenure (formatted with capitalization)
 * - Infra Configuration (formatted with capitalization)
 * - Price (formatted with currency symbol)
 * - Actions (custom action buttons)
 *
 * @type {PlanTableColumn[]}
 */
export const getPlanTableColumns = (refetchPlans: () => void): PlanTableColumn[] => [
  {
    header: 'Plan name',
    accessorKey: 'name',
    id: 'name',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => (
      <EllipsisText text={row.original.name && row.original.name.length > 0 ? row.original.name : '-'} />
    ),
  },
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: (context: CellContext<PlanTableRow, unknown>) => {
      const status = context.getValue() as PlanStatus;
      return <PlanStatusChip status={status} />;
    },
  },
  {
    header: 'No. of devices',
    accessorKey: 'configureDevice',
    id: 'configureDevice',
    cell: ({row}: CellContext<PlanTableRow, unknown>) =>
      row.original.configureDevice ? `${row.original.configureDevice.min} - ${row.original.configureDevice.max}` : '-',
  },
  {
    header: 'Software level',
    accessorKey: 'softwareLevel',
    id: 'softwareLevel',
    cell: ({row}: CellContext<PlanTableRow, unknown>) =>
      row.original.softwareLevel ? `${row.original.softwareLevel.level}` : '',
  },
  {
    header: 'Subscription tenure',
    accessorKey: 'billingCycle',
    id: 'billingCycle',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => {
      if (!row.original.billingCycle) return 'N/A';
      const name = capitalize(row.original.billingCycle?.cycleName) ?? '-';
      return name;
    },
  },
  {
    header: 'Infra configuration',
    accessorKey: 'tier',
    id: 'tier',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => capitalize(row.original.tier) ?? '-',
  },
  {
    header: 'Price',
    accessorKey: 'price',
    id: 'price',
    cell: ({row}: CellContext<PlanTableRow, unknown>) => {
      const {price} = row.original;
      const {currency} = row.original;
      return `${currency?.symbol ?? ''}${price}`;
    },
  },
  {
    header: 'Created Date',
    accessorKey: 'name',
    id: 'createdDate',
    cell: () => '2024-01-01',
  },
  {
    header: 'Modified Date',
    accessorKey: 'name',
    id: 'modifiedDate',
    cell: () => '2024-01-15',
  },
  {
    header: 'Created By',
    accessorKey: 'name',
    id: 'createdBy',
    cell: () => 'Admin User',
  },
  {
    header: 'Plan Version',
    accessorKey: 'name',
    id: 'version',
    cell: () => 'v1.0',
  },
  {
    header: 'Actions',
    cell: (cellContext: CellContext<PlanTableRow, unknown>) => (
      <ActionButtons row={cellContext as CellContext<unknown, unknown>} refetchPlans={refetchPlans} />
    ),
  },
];
