// src/Pages/PlansPage/PlanStatus.test.tsx
import {screen} from '@testing-library/react';
import {memoryRenderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it} from 'vitest';
import PlanStatusChip from './PlanStatus';
import {PlanStatus} from './plans.utils';

describe('PlanStatusChip', () => {
  it('renders ACTIVE status chip (enum)', () => {
    memoryRenderWithTheme(<PlanStatusChip status={PlanStatus.ACTIVE} />);
    expect(screen.getByTestId('status-chip')).toBeInTheDocument();
  });

  it('renders INACTIVE status chip (enum)', () => {
    memoryRenderWithTheme(<PlanStatusChip status={PlanStatus.INACTIVE} />);
    expect(screen.getByTestId('status-chip')).toBeInTheDocument();
  });

  it('renders ACTIVE status chip (stringified enum)', () => {
    memoryRenderWithTheme(<PlanStatusChip status={String(PlanStatus.ACTIVE)} />);
    expect(screen.getByTestId('status-chip')).toBeInTheDocument();
  });

  it('renders INACTIVE status chip (stringified enum)', () => {
    memoryRenderWithTheme(<PlanStatusChip status={String(PlanStatus.INACTIVE)} />);
    expect(screen.getByTestId('status-chip')).toBeInTheDocument();
  });

  it('renders Trial status chip (stringified enum)', () => {
    memoryRenderWithTheme(<PlanStatusChip status={String(PlanStatus.TRIAL)} />);
    expect(screen.getByTestId('status-chip')).toBeInTheDocument();
  });

  it('renders nothing for unknown status', () => {
    memoryRenderWithTheme(<PlanStatusChip status="UNKNOWN" />);
    expect(screen.queryByTestId('status-chip')).not.toBeInTheDocument();
  });

  it('renders nothing for empty string', () => {
    memoryRenderWithTheme(<PlanStatusChip status="" />);
    expect(screen.queryByTestId('status-chip')).not.toBeInTheDocument();
  });
});
