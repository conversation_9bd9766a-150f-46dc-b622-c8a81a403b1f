import StatusChip from 'Components/StatusChip/StatusChip';
import {isNil} from 'lodash';
import {JSX} from 'react';
import {PlanStatus} from './plans.utils';

const whiteMain = 'white.main';
const getFontColor = (status: PlanStatus): string => {
  const statusColorMap: Record<PlanStatus, string> = {
    [PlanStatus.ACTIVE]: `alert.success.onBg`,
    [PlanStatus.INACTIVE]: `alert.error.onBg`,
    [PlanStatus.TRIAL]: `alert.warning.onBg`,
  };
  return statusColorMap[status] || whiteMain;
};

const getIndicatorColor = (status: PlanStatus): string => {
  const statusColorMap: Record<PlanStatus, string> = {
    [PlanStatus.ACTIVE]: 'alert.success.main',
    [PlanStatus.INACTIVE]: 'alert.error.main',
    [PlanStatus.TRIAL]: 'alert.warning.main',
  };
  return statusColorMap[status] || whiteMain;
};

const getBackgroundColor = (status: PlanStatus): string => {
  const statusColorMap: Record<number, string> = {
    [PlanStatus.ACTIVE]: `alert.success.bg`,
    [PlanStatus.INACTIVE]: `alert.error.bg`,
    [PlanStatus.TRIAL]: `alert.warning.bg`,
  };
  return statusColorMap[status] || whiteMain;
};
const getStatusLabel = (status: PlanStatus | number): string => {
  const statusLabelMap: Record<PlanStatus | number, string> = {
    [PlanStatus.ACTIVE]: 'Active',
    [PlanStatus.INACTIVE]: 'Inactive',
    [PlanStatus.TRIAL]: 'Trial',
  };
  return statusLabelMap[status] || '';
};

const planStatusChip = (status: PlanStatus): JSX.Element | null => {
  const backgroundColor = getBackgroundColor(status);
  const color = getFontColor(status);
  const indicatorColor = getIndicatorColor(status);
  const label = getStatusLabel(status);
  return <StatusChip label={label} backgroundColor={backgroundColor} indicatorColor={indicatorColor} color={color} />;
};

/**
 * Renders a status chip for a given plan status.
 *
 * This component maps the provided `status` (which can be a `PlanStatus` enum value or a string)
 * to the appropriate `StatusChipState` and displays a `StatusChip` accordingly.
 *
 * @param props.status - The status of the plan, either as a `PlanStatus` enum value or a string representation.
 * @returns A React fragment containing the `StatusChip` if the status is recognized, otherwise `null`.
 */
const PlanStatusChip: React.FC<{status: PlanStatus | string}> = ({status}) => {
  let mappedStatus: PlanStatus | undefined;
  if (typeof status === 'string') {
    switch (status.toLowerCase()) {
      case String(PlanStatus.ACTIVE).toLowerCase():
        mappedStatus = PlanStatus.ACTIVE;
        break;
      case String(PlanStatus.INACTIVE).toLowerCase():
        mappedStatus = PlanStatus.INACTIVE;
        break;
      case String(PlanStatus.TRIAL).toLowerCase():
        mappedStatus = PlanStatus.TRIAL;
        break;
      default:
        mappedStatus = undefined; // for sonar
    }
  } else {
    mappedStatus = status;
  }
  return <> {!isNil(mappedStatus) && planStatusChip(mappedStatus)} </>;
};

export default PlanStatusChip;

PlanStatusChip.displayName = 'PlanStatusChip';
