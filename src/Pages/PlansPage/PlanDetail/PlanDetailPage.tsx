import {MoreHoriz} from '@mui/icons-material';
import {Box, Divider, Grid, ListItemText, Menu, MenuItem, Typography} from '@mui/material';
import EditIcon from 'Assets/EditIcon';
import BorderButton from 'Components/BorderButton/BorderButton';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import CenterLoaderContainer from 'Components/CenterLoaderContainer';
import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import PermissionWrapper from 'Components/PermissionWrapper';
import {defaultDateFormat} from 'Constants/enums';
import PermissionKey from 'Constants/enums/permissions';
import {Integers} from 'Helpers/integers';
import {formatCurrency} from 'Helpers/utils';
import {capitalize, isNil} from 'lodash';
import {useState} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {useGetPlanByIdQuery} from 'redux/app/planManagementAPiSlice';
import {ISoftwareLevel} from 'redux/app/types/plan.type';
import {RouteNameBuilder, RouteNames} from 'Routes/routeNames';
import {detailPageSubtitleLabelSx, detailPageTitleLabelSx, gridSize} from 'styles/pages/Common.styles';
import {ActionTypes, ActivateDeactivateDialog} from '../ActivateDeactivateDialog/ActivateDeactivateDialog';
import {useActivationDeactivationState} from '../ActivateDeactivateDialog/useActivationDeactivateDialogState';
import {baseFilterForPlanDetail, getCombinedPlanTierName, getPlanTierType, PlanStatus} from '../plans.utils';
import PlanStatusChip from '../PlanStatus';
import PlanVersionHistory from '../PlanVersionHistory';
const PX = 1.5;

/**
 * Displays detailed information about a specific plan, including its status, pricing, configuration, and version history.
 *
 * This page allows users with the appropriate permissions to edit, activate, or deactivate the plan.
 * It fetches plan data based on the `planId` from the route parameters and handles loading and error states.
 *
 * Features:
 * - Shows plan details such as version, price, billing cycle, device limits, infra configuration, user cost, and timestamps.
 * - Provides breadcrumbs for navigation context.
 * - Allows editing and activation/deactivation of the plan via menu actions, if the user has the required permissions.
 * - Displays a dialog for confirming activation or deactivation actions.
 * - Includes a close button to navigate back.
 *
 * @component
 * @returns {JSX.Element} The rendered plan detail page.
 */
const PlanDetailPage = () => {
  const navigate = useNavigate();
  const {hasPermission} = usePermissions();
  const {planId} = useParams<{planId: string}>();

  const {
    data: plan,
    isLoading: loadingPlan,
    error: planError,
    refetch: refetchPlans,
  } = useGetPlanByIdQuery(
    {
      planId: planId ?? '',
      filter: {
        ...baseFilterForPlanDetail,
      },
    },
    {
      skip: !planId,
    },
  );

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const isActive = plan?.status === String(PlanStatus.ACTIVE);

  const {
    openActivateDeactivate,
    setOpenActivateDeactivate,
    actionType,
    setActionType,
    isUpdatingStatus,
    handleActivateDeactivate,
  } = useActivationDeactivationState({
    refetchPlans: () => {
      refetchPlans();
    },
  });
  const isTrialPlan = plan?.status === String(PlanStatus.TRIAL);
  const handleDialogClose = () => {
    setOpenActivateDeactivate(false);
  };

  const breadcrumbs = () => {
    return [
      {label: 'Plans', url: RouteNames.PLANS},
      {
        label: plan?.name ?? '-',
        url: '#',
      },
    ];
  };
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const buildTopPart = () => {
    if (!plan) return null;
    return (
      <Box display="flex" alignItems="center" gap={1}>
        <Box display={'flex'} flexDirection={'column'} gap={1}>
          <Box display={'flex'} flexDirection={'row'} alignItems="center" gap={1}>
            <Typography sx={{fontSize: '1.25rem', fontWeight: 700}}>{plan.name ?? ''}</Typography>
            <PlanStatusChip status={plan.status} />
          </Box>
          <Breadcrumb items={breadcrumbs()} separator="|" />
        </Box>
        <Box sx={{ml: 'auto', display: 'flex', flexDirection: 'row', gap: 1}}>
          {hasPermission(PermissionKey.UpdatePlan) && plan.status === String(PlanStatus.ACTIVE) && (
            <BorderButton
              sx={{fontWeight: 700, px: '1.56rem', height: '2.5rem'}}
              onClick={() => {
                navigate(RouteNameBuilder.buildEditPlan(plan.id));
              }}
            >
              <Box sx={{display: 'flex', alignItems: 'center', gap: 0.5}}>
                <EditIcon sx={{fill: 'white', width: '1.2rem'}} />
                Edit
              </Box>
            </BorderButton>
          )}

          {/* add menu button having deactivate user button in it  */}
          {!isTrialPlan && (
            <PermissionWrapper permission={PermissionKey.UpdatePlan}>
              <BorderButton sx={{px: 0, minWidth: 0, width: '2.5rem', height: '2.5rem'}} onClick={handleMenuOpen}>
                <MoreHoriz sx={{fill: theme => theme.palette.body[Integers.FiveHundred]}} />
              </BorderButton>
            </PermissionWrapper>
          )}
        </Box>
      </Box>
    );
  };
  const buildBottomSection = () => {
    return (
      <Box sx={{display: 'flex', flexDirection: 'column', mt: 'auto', mb: 1.5}}>
        <Divider sx={{my: 2}} />
        <Box sx={{alignSelf: 'flex-end'}} px={PX}>
          <BorderButton
            sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
            fullWidth={false}
            onClick={() => navigate(-1)}
          >
            Close
          </BorderButton>
        </Box>
      </Box>
    );
  };

  if (loadingPlan) {
    return (
      <CenterLoaderContainer isLoading={true}>
        <Box></Box>
      </CenterLoaderContainer>
    );
  }

  if (planError) {
    return (
      <Box p={4} textAlign="center" color="error.main">
        Failed to load plan details.
      </Box>
    );
  }

  const planTier = getPlanTierType(plan?.tier);
  const getSoftwareLevelValue = (softwareLevel: ISoftwareLevel | undefined) => capitalize(softwareLevel?.level ?? '-');

  const dataArray = (() => [
    {
      label: 'Current version',
      value: plan?.version ?? '-',
    },
    {
      label: 'Price',
      value: formatCurrency(plan?.price ?? '-'),
    },
    {
      label: 'Subscription tenure',
      value: capitalize(plan?.billingCycle?.cycleName ?? '-'),
    },
    {
      label: 'Software level',
      value: getSoftwareLevelValue(plan?.softwareLevel),
    },
    {
      label: 'No. of devices',
      value: plan?.configureDevice ? `${plan.configureDevice.min} - ${plan.configureDevice.max}` : '-',
    },
    {
      label: 'Infra configuration',
      value: planTier ? getCombinedPlanTierName(planTier) : '-',
    },
    {
      label: 'Users',
      value: isNil(plan?.costPerUser) ? 'unlimited' : formatCurrency(plan?.costPerUser ?? '-'),
    },
    {
      label: 'Created date',
      value: plan?.createdOn ? defaultDateFormat(plan.createdOn) : '-',
    },
    {
      label: 'Modified date',
      value: plan?.modifiedOn ? defaultDateFormat(plan.modifiedOn) : '-',
    },
  ])();

  const buildMenu = () => (
    <Menu
      id="user-menu"
      anchorEl={anchorEl}
      open={open}
      onClose={handleMenuClose}
      anchorOrigin={{
        vertical: 'bottom',
        horizontal: 'right',
      }}
      transformOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
    >
      <MenuItem
        onClick={() => {
          setActionType(isActive ? ActionTypes.Deactivate : ActionTypes.Activate);
          setOpenActivateDeactivate(true);
          setAnchorEl(null);
        }}
      >
        <ListItemText>{isActive ? 'Deactivate' : 'Activate'} </ListItemText>
      </MenuItem>
    </Menu>
  );

  const buildDialog = () => {
    return (
      <>
        {actionType && plan && (
          <ActivateDeactivateDialog
            actionType={actionType as 'Activate' | 'Deactivate'}
            onConfirm={() => handleActivateDeactivate(actionType, plan.id, plan.name)}
            open={openActivateDeactivate}
            onClose={handleDialogClose}
            isLoading={isUpdatingStatus}
            title={plan.name}
          />
        )}
      </>
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 0.5,
      }}
    >
      {buildTopPart()}
      {/* Header */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1.5,
        }}
      >
        <Box
          sx={{
            border: '1px solid',
            borderColor: 'body.200',
            borderRadius: '0.375rem',
            pt: 1,
            minHeight: '80vh',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <Typography sx={{fontSize: '1rem', fontWeight: 700, color: 'body:900'}} px={PX}>
            Plan Information
          </Typography>

          <Grid container spacing={2} px={PX} mt={1}>
            {dataArray.map((data, index) => (
              <Grid size={{...gridSize, xs: 6}} key={'data' + index}>
                <Typography sx={detailPageTitleLabelSx}>{data.label}</Typography>
                <Typography sx={detailPageSubtitleLabelSx}>{data.value}</Typography>
              </Grid>
            ))}
          </Grid>
          <Grid
            container
            rowSpacing={2}
            spacing={2}
            size={12}
            sx={{px: PX, pt: 2}}
            display={isTrialPlan ? 'none' : 'flex'}
          >
            <PlanVersionHistory injectedValues={plan} minExpandedHeight={'45vh'} />
          </Grid>

          {buildBottomSection()}
        </Box>
        {!isTrialPlan && buildMenu()}
      </Box>
      {buildDialog()}
    </Box>
  );
};

PlanDetailPage.displayName = 'PlanDetailPage';

export default PlanDetailPage;
