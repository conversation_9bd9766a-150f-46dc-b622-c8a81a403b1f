import {Box, Grid, InputAdornment, Stack, Typography, useTheme} from '@mui/material';
import {CustomSwitchButton} from 'Components/CustomSwitch';
import FormInput from 'Components/Forms/FormInput';
import FormSelect from 'Components/Forms/FormSelect/FormSelect';
import {useFormikContext} from 'formik';
import {StyleUtils} from 'Helpers/styleUtils';
import React from 'react';
import {ICurrency, PlanResponse} from 'redux/app/types/plan.type';
import {
  formHeaderFirstStyles,
  formHeaderStyles,
  inputAdornment,
  inputBoxForAdornmentStyles,
  toggleHeaderStyles,
  toggleTextStyles,
} from 'styles/pages/AddPlan.styles';
import {FormAddPlan} from './AddPlan';
import {groupedFeatureOptions} from './plans.utils';
import PlanVersionHistory from './PlanVersionHistory';
interface PlanFormFieldsProps {
  isEdit: boolean;
  deviceOptions?: Array<{value: string; label: string}>;
  softwareLevelOptions?: Array<{value: string; label: string}>;
  tenureOptions?: Array<{value: string; label: string}>;
  currencyCodeOptions?: ICurrency;
  injectedValues?: PlanResponse;
}
const sanitizeDecimalInput = (value: string) => {
  if (!value) return '';
  // remove everything except digits and dot
  let v = value.replace(/[^0-9.]/g, '');
  // keep only first dot if user typed many
  const parts = v.split('.');
  if (parts.length > 1) {
    const intPart = parts[0];
    const decPart = parts.slice(1).join(''); // join any extra dots parts
    v = `${intPart}.${decPart}`;
  }
  // limit decimals to max 2
  if (v.includes('.')) {
    const [intPart, decPart] = v.split('.');
    v = `${intPart}.${decPart.slice(0, 2)}`;
  }
  return v;
};

function getEditModeStyles() {
  return {
    backgroundColor: '#E9E9F1',
    '& .MuiOutlinedInput-notchedOutline': {
      borderColor: '#C0CEEA',
    },
  };
}

/**
 * Renders the form fields for creating or editing a plan, including plan details,
 * configuration options, pricing, and user cost settings.
 *
 * @component
 * @param {PlanFormFieldsProps} props - The props for the component.
 * @param {boolean} props.isEdit - Indicates if the form is in edit mode (fields are read-only).
 * @param {Array<SelectOption>} props.deviceOptions - Options for the number of devices.
 * @param {Array<SelectOption>} props.tenureOptions - Options for the subscription tenure.
 * @param {CurrencyCodeOptions} props.currencyCodeOptions - Currency symbol and related options.
 *
 * @returns {JSX.Element} The rendered form fields for the plan.
 *
 * @remarks
 * - Uses Formik context for form state management.
 * - Includes a toggle for unlimited users, which conditionally disables the "cost per user" input.
 * - Supports grouped feature options for infra configuration.
 */
const PlanFormFields: React.FC<PlanFormFieldsProps> = ({
  isEdit,
  deviceOptions,
  tenureOptions,
  currencyCodeOptions,
  softwareLevelOptions,
  injectedValues,
}) => {
  const theme = useTheme();
  const {values, setFieldValue} = useFormikContext<FormAddPlan>();
  const allowedUnlimitedUsers = values.allowedUnlimitedUsers;
  const editModeStyles = isEdit ? getEditModeStyles() : {};

  const buildEdit = () => isEdit && <PlanVersionHistory injectedValues={injectedValues} />;

  return (
    <Grid container rowSpacing={2} spacing={2} size={12}>
      <Grid size={{xs: 12}}>
        <Typography variant="h6" sx={formHeaderFirstStyles}>
          Plan details
        </Typography>
      </Grid>

      {/* Plan Name */}
      <Grid size={{xs: 12, sm: 4, md: 4}}>
        <Typography sx={StyleUtils.lalelStyles}>Plan name *</Typography>
        <FormInput
          fullWidth
          id="name"
          name="name"
          required
          sx={{...StyleUtils.inputBoxStyles, ...editModeStyles}}
          readOnly={isEdit}
          placeholder="Enter plan name"
        />
      </Grid>

      <Grid size={{xs: 12}}>
        <Typography variant="h6" sx={formHeaderStyles}>
          Plan configuration
        </Typography>
      </Grid>

      {/* Subscription level */}
      <Grid size={{xs: 12, sm: 4, md: 4}}>
        <Typography sx={StyleUtils.lalelStyles}>Software level *</Typography>
        <FormSelect
          fullWidth
          id="softwareLevelId"
          name="softwareLevelId "
          placeholder="Select software level"
          required
          sx={{...StyleUtils.selectBoxStyles, ...editModeStyles}}
          options={softwareLevelOptions ?? []}
          readOnly={isEdit}
          placeholderSx={{color: 'body.300'}}
        />
      </Grid>
      {/* No. of devices */}
      <Grid size={{xs: 12, sm: 4, md: 4}}>
        <Typography sx={StyleUtils.lalelStyles}>No. of devices *</Typography>
        <FormSelect
          fullWidth
          id="configureDeviceId"
          name="configureDeviceId"
          placeholder="Select no. of devices"
          required
          sx={{...StyleUtils.selectBoxStyles, ...editModeStyles}}
          options={deviceOptions ?? []}
          readOnly={isEdit}
          placeholderSx={{color: 'body.300'}}
        />
      </Grid>

      {/* Infra configuration */}
      <Grid size={{xs: 12, sm: 4, md: 4}}>
        <Typography sx={StyleUtils.lalelStyles}>Infra configuration *</Typography>
        <FormSelect
          fullWidth
          id="tier"
          name="tier"
          placeholder="Select infra configuration"
          required
          sx={{...StyleUtils.selectBoxStyles, ...editModeStyles}}
          options={groupedFeatureOptions}
          readOnly={isEdit}
          placeholderSx={{color: theme.palette.body[300]}}
          renderOption={option => (
            <Box>
              <Typography variant="body1" fontWeight={500}>
                {option.label}
              </Typography>
              {'features' in option && Array.isArray(option.features) && (
                <Typography variant="body2" color="body.500" sx={{whiteSpace: 'pre-line'}}>
                  {(option.features as string[]).join(', ')}
                </Typography>
              )}
            </Box>
          )}
          renderValue={(selected, options) => {
            const option = options.find(opt => opt.value === selected);
            if (option && 'features' in option && Array.isArray(option.features)) {
              return `${option.label} (${option.features.join(', ')})`;
            }
            return option ? option.label : selected;
          }}
        />
      </Grid>

      {/* Subscription Tenure */}
      <Grid size={{xs: 12, sm: 4, md: 4}}>
        <Typography sx={StyleUtils.lalelStyles}>Subscription tenure *</Typography>
        <FormSelect
          fullWidth
          id="billingCycleId"
          name="billingCycleId"
          placeholder="Select subscription tenure"
          required
          sx={{...StyleUtils.selectBoxStyles, ...editModeStyles}}
          options={tenureOptions ?? []}
          placeholderSx={{color: theme.palette.body[300]}}
          readOnly={isEdit}
        />
      </Grid>

      {/* Price */}
      <Grid size={{xs: 12, sm: 4, md: 4}}>
        <Typography sx={StyleUtils.lalelStyles}>Price *</Typography>
        <FormInput
          startAdornment={
            <InputAdornment position="start" sx={inputAdornment}>
              {currencyCodeOptions?.symbol}
            </InputAdornment>
          }
          fullWidth
          id="price"
          name="price"
          onInput={e => {
            const target = e.target as HTMLInputElement;
            target.value = sanitizeDecimalInput(target.value);
          }}
          required
          sx={inputBoxForAdornmentStyles}
          placeholder="Enter price"
          inputProps={{inputMode: 'decimal', maxLength: 8}}
        />
      </Grid>

      <Grid size={{xs: 12}}>
        <Typography variant="h6" sx={formHeaderStyles}>
          Cost per user
        </Typography>
      </Grid>

      {/* Toggle Button */}
      <Grid size={{xs: 12, sm: 12}}>
        <Stack direction="row" spacing={2} sx={{alignItems: 'flex-start'}}>
          <Box sx={{pt: 0.5}}>
            <CustomSwitchButton
              checked={allowedUnlimitedUsers}
              data-testid="unlimited-users-toggle"
              onChange={(_, checked) => setFieldValue('allowedUnlimitedUsers', checked)}
            />
          </Box>
          <Stack direction="column" spacing={0.5} sx={{flex: 1, marginLeft: '0 !important'}}>
            <Typography variant="body1" sx={toggleHeaderStyles}>
              Unlimited users
            </Typography>
            <Typography variant="body2" sx={toggleTextStyles}>
              Enable this option to allow unlimited users under this plan without additional cost per user.
            </Typography>
          </Stack>
        </Stack>
      </Grid>

      {/* Always render the Grid, but hide the input visually when toggleEnabled is true */}
      <Grid size={{xs: 12, sm: 4}}>
        <Box
          sx={{
            visibility: allowedUnlimitedUsers ? 'hidden' : 'visible',
            display: allowedUnlimitedUsers ? 'none' : 'block',
          }}
        >
          <Typography sx={StyleUtils.lalelStyles}>Cost per user *</Typography>
          <FormInput
            startAdornment={
              <InputAdornment position="start" sx={inputAdornment}>
                {currencyCodeOptions?.symbol}
              </InputAdornment>
            }
            fullWidth
            id="costPerUser"
            name="costPerUser"
            onInput={e => {
              const target = e.target as HTMLInputElement;
              target.value = sanitizeDecimalInput(target.value);
            }}
            required={!allowedUnlimitedUsers}
            sx={inputBoxForAdornmentStyles}
            placeholder="Enter cost per user"
            disabled={allowedUnlimitedUsers}
            inputProps={{inputMode: 'decimal', maxLength: 6}}
          />
        </Box>
      </Grid>

      {buildEdit()}
    </Grid>
  );
};
export default PlanFormFields;
