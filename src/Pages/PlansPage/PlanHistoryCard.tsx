import {Avatar, Card, Grid, Stack, SxProps, Typography} from '@mui/material';
import {skipToken} from '@reduxjs/toolkit/query';
import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import PermissionKey from 'Constants/enums/permissions';
import {isNil} from 'lodash';
import {useGetUserByIdQuery} from 'redux/app/tenantManagementApiSlice';
import {UserViewType} from 'redux/app/types';
import PlanStatusChip from './PlanStatus';

interface PlanData {
  name: string;
  numberOfDevices: string;
  softwareLevel?: string;
  infraConfigurations: string;
  tenure: string;
  price: string;
  costPerUser: string;
  allowedUnlimited: boolean;
  version: string;
  createdAt: string;
  createdBy: string;
  _priceChanged?: boolean;
  _usersChanged?: boolean;
  status?: string;
}

interface PlanHistoryCardProps {
  data: PlanData;
  updatedBy?: string;
  index?: number;
  sx?: SxProps;
}

function getAvatar(userData: UserViewType[] | undefined) {
  return userData && userData.length > 0 && userData[0]
    ? `${userData[0].firstName?.[0] ?? ''}${userData[0].lastName?.[0] ?? ''}`.toUpperCase()
    : '';
}

export function PlanHistoryCard({data, updatedBy, index, sx}: Readonly<PlanHistoryCardProps>) {
  const {hasPermission} = usePermissions();
  const hasUserViewPermission = hasPermission(PermissionKey.ViewTenantUser);
  const {data: userData} = useGetUserByIdQuery(updatedBy ?? skipToken, {
    skip: !hasUserViewPermission,
  });
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    if (Number.isNaN(date.getTime())) return ''; // or throw new Error('invalid date')

    const parts = new Intl.DateTimeFormat('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    }).formatToParts(date);

    const day = parts.find(p => p.type === 'day')?.value ?? '';
    const month = parts.find(p => p.type === 'month')?.value ?? '';
    const year = parts.find(p => p.type === 'year')?.value ?? '';

    return `${day} ${month}, ${year}`; // e.g. "2 Jul, 2025"
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date
      .toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      })
      .replace(/\s?(AM|PM)$/, match => match.toLowerCase());
  };

  const formatTenure = (tenure: string) => tenure.charAt(0).toUpperCase() + tenure.slice(1).toLowerCase();

  const formatPrice = (price: string) => `$${Number.parseFloat(price).toFixed(2)}`;

  const formatUsers = (costPerUser: string, allowedUnlimited: boolean) => {
    if (allowedUnlimited) {
      return 'Unlimited users';
    }
    return `$${Number.parseFloat(costPerUser).toFixed(2)} per user`;
  };

  const getSoftwareLevel = (softwareLevel: string | undefined) => formatTenure(softwareLevel ?? '');

  return (
    <Grid container sx={{...sx}}>
      <Grid size={{xs: 12}} sx={{paddingBottom: '0.625rem', paddingLeft: '0.75rem'}}>
        <Typography sx={{fontSize: '0.8125rem'}}>
          <span style={{fontWeight: '800', color: 'body.600'}}>{data.version.toUpperCase()}</span>{' '}
          <span style={{fontWeight: '700', color: 'body.600'}}>
            • {formatDate(data.createdAt)} • {formatTime(data.createdAt)}
          </span>
        </Typography>
      </Grid>
      <Grid size={{xs: 12}}>
        <Card
          sx={{
            minHeight: '11.125rem',
            transform: 'rotate(0deg)',
            opacity: 1,
            padding: '14px',
            borderRadius: '6px',
          }}
        >
          <Grid container size={{xs: 12}}>
            <Grid size={{xs: 8}}>
              <Stack direction="row" alignItems="center" spacing={1}>
                <Typography>{data.name}</Typography>

                {index === 0 && !isNil(data.status) && <PlanStatusChip status={data.status} />}
              </Stack>
            </Grid>

            {hasUserViewPermission && (
              <Grid size={{xs: 4}} sx={{display: 'flex', justifyContent: 'flex-end', alignItems: 'center'}}>
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Typography variant="body2" color="body.500">
                    Updated by:
                  </Typography>

                  <Avatar
                    sx={{
                      width: 24,
                      height: 24,
                      fontSize: '0.75rem',
                      bgcolor: '#C0CEEA',
                      color: '#173478',
                    }}
                  >
                    {getAvatar(userData)}
                  </Avatar>

                  <Typography variant="body2" color="body.500">
                    {`${userData?.[0]?.firstName ?? ''} ${userData?.[0]?.lastName ?? ''} • ${userData?.[0]?.roleName ?? ''}`}
                  </Typography>
                </Stack>
              </Grid>
            )}
          </Grid>

          <Grid container spacing={0.75} size={{xs: 12}} sx={{paddingTop: '1rem'}}>
            {[
              {
                id: '1',
                label: 'No. of devices',
                value: data.numberOfDevices,
              },
              {
                id: '2',
                label: 'Infra configuration',
                value: data.infraConfigurations.charAt(0).toUpperCase() + data.infraConfigurations.slice(1),
              },
              {
                id: '3',
                label: 'Subscription tenure',
                value: formatTenure(data.tenure),
              },
              {
                id: '4',
                label: 'Software level',
                value: getSoftwareLevel(data.softwareLevel),
              },
              {
                id: '5',
                label: 'Price',
                value: formatPrice(data.price),
                // apply bg only when _priceChanged is true
                bg: data._priceChanged ? '#F7ECF1' : undefined,
              },
              {
                id: '6',
                label: 'Users',
                value: formatUsers(data.costPerUser, data.allowedUnlimited),
                bg: data._usersChanged ? '#F7ECF1' : undefined,
              },
            ].map(item => (
              <Grid
                key={item.id ?? item.label}
                size={{xs: 4}}
                sx={{
                  height: '49px',
                  transform: 'rotate(0deg)',
                  opacity: 1,
                  p: '6px',
                  borderRadius: '4px',
                  backgroundColor: item.bg || 'transparent',
                }}
              >
                <Typography sx={{fontWeight: 600, fontSize: '0.75rem', color: 'body.500'}}>{item.label}</Typography>
                <Typography sx={{fontWeight: 600, fontSize: '0.8125rem', color: 'body.800'}}>{item.value}</Typography>
              </Grid>
            ))}
          </Grid>
        </Card>
      </Grid>
    </Grid>
  );
}
