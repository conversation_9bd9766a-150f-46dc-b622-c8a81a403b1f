import {CellContext} from '@tanstack/react-table';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import StatusChip from 'Components/StatusChip/StatusChip';
import {StatusChipState} from 'Components/StatusChip/statusChip.util';
import {dateFormatter} from 'Helpers/utils';
import {TenantType} from 'redux/app/types';
import {ActionButtons} from './TenantPage';

export const DEFAULT_LIMIT = 5;
export const DEFAULT_OFFSET = 0;

export enum TenantStatus {
  ACTIVE, // Tenant is active and fully functional
  PENDINGPROVISION, // Tenant is awaiting provisioning
  PROVISIONING, // Tenant is currently being provisioned
  PROVISIONFAILED, // Provisioning process failed
  INACTIVE, // Tenant is inactive
  PENDINGONBOARDING, // Tenant is active but not onboarded due to missing information
  TENANTSUSPENDED,
  TENANTOVERDUE,
  PENDINGPROVISIONEXPIRED,
  DEPROVISIONING,
  SUSPENDING,
  REACTIVATING,
  DEPROVI<PERSON>ON<PERSON><PERSON><PERSON>,
  S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>IL<PERSON>,
  REACT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  MI<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  PEN<PERSON>NGRE<PERSON>TIVATION,
}

export const tenantStatusMapToStatusChip: Record<TenantStatus, StatusChipState> = {
  [TenantStatus.ACTIVE]: StatusChipState.ACTIVE,
  [TenantStatus.PENDINGPROVISION]: StatusChipState.PENDINGPROVISION,
  [TenantStatus.PROVISIONING]: StatusChipState.PROVISIONING,
  [TenantStatus.PROVISIONFAILED]: StatusChipState.PROVISIONFAILED,
  [TenantStatus.INACTIVE]: StatusChipState.INACTIVE,
  [TenantStatus.PENDINGONBOARDING]: StatusChipState.PENDINGONBOARDING,
  [TenantStatus.TENANTSUSPENDED]: StatusChipState.TENANTSUSPENDED,
  [TenantStatus.TENANTOVERDUE]: StatusChipState.TENANTOVERDUE,
  [TenantStatus.PENDINGPROVISIONEXPIRED]: StatusChipState.PENDINGPROVISIONEXPIRED,
  [TenantStatus.DEPROVISIONING]: StatusChipState.DEPROVISIONING,
  [TenantStatus.SUSPENDING]: StatusChipState.SUSPENDING,
  [TenantStatus.REACTIVATING]: StatusChipState.REACTIVATING,
  [TenantStatus.DEPROVISIONFAILED]: StatusChipState.DEPROVISIONFAILED,
  [TenantStatus.SUSPENSIONFAILED]: StatusChipState.SUSPENSIONFAILED,
  [TenantStatus.REACTIVATIONFAILED]: StatusChipState.REACTIVATIONFAILED,
  [TenantStatus.MIGRATING]: StatusChipState.MIGRATING,
  [TenantStatus.MIGRATIONFAILED]: StatusChipState.MIGRATIONFAILED,
  [TenantStatus.PENDINGREACTIVATION]: StatusChipState.PENDING_REACTIVATION,
};

export const getStatusLabel = (status: TenantStatus | number): string => {
  const statusLabelMap: Record<TenantStatus | number, string> = {
    [TenantStatus.ACTIVE]: 'Active',
    [TenantStatus.PENDINGPROVISION]: 'Pending Provision',
    [TenantStatus.INACTIVE]: 'Inactive',
    [TenantStatus.PROVISIONFAILED]: 'Provision Failed',
    [TenantStatus.PROVISIONING]: 'Provisioning',
    [TenantStatus.PENDINGONBOARDING]: 'Pending Onboarding',
    [TenantStatus.TENANTSUSPENDED]: 'Suspended',
    [TenantStatus.TENANTOVERDUE]: 'Overdue',
    [TenantStatus.PENDINGPROVISIONEXPIRED]: 'Pending Provision Expired',
    [TenantStatus.DEPROVISIONING]: 'Deprovisioning',
    [TenantStatus.SUSPENDING]: 'Suspending',
    [TenantStatus.REACTIVATING]: 'Reactivating',
    [TenantStatus.DEPROVISIONFAILED]: 'Deprovision Failed',
    [TenantStatus.SUSPENSIONFAILED]: 'Suspension Failed',
    [TenantStatus.REACTIVATIONFAILED]: 'Reactivation Failed',
    [TenantStatus.MIGRATING]: 'Migrating',
    [TenantStatus.MIGRATIONFAILED]: 'Migration Failed',
    [TenantStatus.PENDINGREACTIVATION]: 'Pending Reactivation',
  };
  return statusLabelMap[status] || '';
};

interface TenantTableRow extends TenantType {}

interface TenantTableColumn {
  header: string;
  accessorKey?: keyof TenantTableRow;
  id?: string;
  cell?: (context: CellContext<TenantTableRow, unknown>) => React.ReactNode;
}

const columnNameMap: Record<string, string> = {
  tenantName: 'name',
  status: 'status',
  createdDate: 'createdOn',
  planName: 'name',
};

export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

export const tenantTableColumns = (refetchTenants: () => void): TenantTableColumn[] => [
  {
    header: 'Tenant name',
    accessorKey: 'name',
    id: 'tenantName',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => (
      <EllipsisText text={row.original.name && row.original.name.length > 0 ? row.original.name : '-'} />
    ),
  },
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: (context: CellContext<TenantTableRow, unknown>) => {
      const status = context.getValue() as TenantStatus;
      const label = getStatusLabel(status);
      return <StatusChip label={label} status={tenantStatusMapToStatusChip[status]} />;
    },
  },
  {
    header: 'Created date',
    accessorKey: 'createdOn',
    id: 'createdDate',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => dateFormatter(row.original.createdOn),
  },
  {
    header: 'Plan name',
    accessorKey: 'planName',
    id: 'planName',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => {
      const planName = row.original.planName;
      return <EllipsisText text={planName && planName.length > 0 ? planName : '-'} />;
    },
  },
  {
    header: 'Actions',
    cell: (cellContext: CellContext<TenantTableRow, unknown>) => (
      <ActionButtons row={cellContext as CellContext<unknown, unknown>} refetchTenants={refetchTenants} />
    ),
  },
];
