import {Box} from '@mui/material';
import BackdropLoader from 'Components/BackdropLoader';
import {PlanStatus} from 'Pages/PlansPage/plans.utils';
import {getStateOptions} from 'Pages/utils';
import {useEffect, useState} from 'react';
import {useParams} from 'react-router-dom';
import {useGetSubscriptionQuery, useGetTenantsQuery} from 'redux/app/tenantManagementApiSlice';
import {Subscription, TenantType} from 'redux/app/types';
import AddTenantPage from '../AddTenant/AddTenantPage';
import {FormAddTenant} from '../AddTenant/addTenantsUtils';
import {InitialChoosePlanValues} from '../AddTenant/PlanSection/ChoosePlan';

/**
 * Checks if both tenants and subscriptions data exist and are valid.
 *
 * @param {TenantType[]} [tenants] - List of tenant objects.
 * @param {Subscription[]} [subscriptions] - List of subscription objects.
 * @returns {boolean} True if both tenants and subscriptions are available.
 */
function hasValidTenantAndSubscription(tenants?: TenantType[], subscriptions?: Subscription[]): boolean {
  return Boolean(tenants?.length && subscriptions?.length);
}

/**
 * Builds the initial form values for the AddTenant form
 * from tenant and subscription data.
 *
 * @param {TenantType} tenant - The tenant object.
 * @param {Subscription} subscription - The subscription object.
 * @returns {FormAddTenant} The initial form values for editing a tenant.
 */
function buildInitialValues(tenant: TenantType, subscription: Subscription, stateId: string): FormAddTenant {
  const {id, name = '', key = '', address = {}, contacts = [], files = [], lang = ''} = tenant;
  const {city = '', state = ''} = address;
  const contact = contacts[0] ?? {};
  const {firstName = '', lastName = '', designation = '', email = '', phoneNumber = '', userName = ''} = contact;

  return {
    id,
    firstName,
    lastName,
    company: name,
    userName,
    designation: designation ?? undefined,
    email,
    mobileNumber: phoneNumber ?? undefined,
    key: key.replace(/\.distek\.com$/, ''),
    city: city,
    state: stateId,
    stateId: state,
    countryCode: {code: '+1', label: 'USA'},
    language: lang,
    files,
    overAllPlan: subscription.plan,
    userCount: subscription.numberOfUsers ?? 0,
    totalCost: subscription.totalCost ?? 0,
  };
}

/**
 * Builds the initial values for the plan selection section
 * from subscription data.
 *
 * @param {Subscription} subscription - The subscription object.
 * @returns {InitialChoosePlanValues} The initial values for ChoosePlan.
 */
function buildPlanInitialValues(subscription: Subscription): InitialChoosePlanValues {
  return {
    billingCycle: subscription.plan?.billingCycleId || '',
    noOfDevices: subscription.plan?.configureDeviceId || '',
    infraConfig: subscription.plan?.tier || '',
    planId: subscription.planId || '',
    userCount: Number(subscription.numberOfUsers) || 1,
    status: Number(subscription.plan.status),
    overAllPlan: subscription.plan,
    subscription: Number(subscription.plan.status) === PlanStatus.TRIAL ? subscription : undefined,
  };
}

/**
 * Extracts the first tenant's ID from a list of tenants.
 *
 * @param {{id: string}[]} [data] - Array of objects containing tenant IDs.
 * @returns {string | undefined} The first tenant ID, or undefined if none exist.
 */
function getFirstTenantId(data?: {id: string}[]): string | undefined {
  return data?.[0]?.id;
}

/**
 * EditTenant Component
 *
 * React component that loads a tenant and its subscription,
 * prepares initial form values, and renders the AddTenantPage
 * in edit mode.
 *
 * @component
 * @returns {JSX.Element} The rendered EditTenant component.
 */
const EditTenant: React.FC = () => {
  const {id} = useParams<{id: string}>();

  const {
    data: tenantData,
    isLoading: tenantLoading,
    error: tenantError,
  } = useGetTenantsQuery(
    {
      where: {id: id || ''},
      include: [{relation: 'files'}, {relation: 'contacts'}, {relation: 'address'}],
    },
    {refetchOnMountOrArgChange: true},
  );

  const [initialValues, setInitialValues] = useState<FormAddTenant | null>(null);
  const [choosePlanInitialValues, setChoosePlanInitialValues] = useState<InitialChoosePlanValues | null>(null);

  // Derive tenantId when tenantData arrives
  const tenantId = getFirstTenantId(tenantData);

  // Subscription filter using tenantId
  const subscriptionFilter = {
    where: {subscriberId: tenantId || ''},
    include: [
      {
        relation: 'plan',
        scope: {
          include: [{relation: 'currency'}, {relation: 'billingCycle'}],
        },
      },
    ],
  };

  // Only call subscription query when we have a tenantId
  const {
    data: subscriptionsData,
    isLoading: subsLoading,
    error: subsError,
  } = useGetSubscriptionQuery(subscriptionFilter, {
    skip: !tenantId,
    refetchOnMountOrArgChange: true,
  });

  useEffect(() => {
    const fetchInitialValues = async () => {
      if (!hasValidTenantAndSubscription(tenantData, subscriptionsData)) return;

      const tenant = tenantData![0];
      const subscription = subscriptionsData![0];

      let stateId = '';
      stateId = await getStateOptions(tenant?.address?.state ?? '');

      setInitialValues(buildInitialValues(tenant, subscription, stateId));
      setChoosePlanInitialValues(buildPlanInitialValues(subscription));
    };

    fetchInitialValues();
  }, [tenantData, subscriptionsData]);

  const waitingForInitial = tenantLoading || !initialValues;
  const waitingForSubs = tenantId ? subsLoading : false;
  const isWaiting = waitingForInitial || waitingForSubs;

  if (tenantError) {
    return (
      <Box p={4} textAlign="center" color="error.main">
        Failed to load tenant details.
      </Box>
    );
  }

  if (subsError) {
    return (
      <Box p={4} textAlign="center" color="error.main">
        Failed to load subscription details.
      </Box>
    );
  }

  if (isWaiting) {
    return <BackdropLoader />;
  }

  return (
    <AddTenantPage
      isEdit
      tenantStatus={tenantData?.[0]?.status}
      initialValues={initialValues}
      choosePlanInitialValues={choosePlanInitialValues}
    />
  );
};

export default EditTenant;
