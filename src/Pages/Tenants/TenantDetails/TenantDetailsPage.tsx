import {Box, Button, Container, Tooltip, Typography, useTheme} from '@mui/material';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import CenterLoaderContainer from 'Components/CenterLoaderContainer';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import PermissionWrapper from 'Components/PermissionWrapper';
import StatusChip from 'Components/StatusChip/StatusChip';
import PermissionKey from 'Constants/enums/permissions';
import {Integers} from 'Helpers/integers';
import {getCombinedPlanTierName, getPlanTierType, PlanStatus} from 'Pages/PlansPage/plans.utils';
import PlanStatusChip from 'Pages/PlansPage/PlanStatus';
import React from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {useGetTenantBillingsQuery} from 'redux/app/invoiceManagementApiSlice';
import {useGetSubscriptionQuery, useGetTenantByIdQuery} from 'redux/app/tenantManagementApiSlice';
import {IFile, TenantType} from 'redux/app/types';
import {ITenantBillingInvoice} from 'redux/app/types/invoice.type';
import {Subscription} from 'redux/app/types/subscription.type';
import EditIcon from '../../../Assets/edit.svg';
import ObservabilityIcon from '../../../Assets/observability.svg';
import CloseButton from '../../../Components/CloseButton/CloseButton';
import DocumentItem from '../../../Components/DocumentItem/DocumentItem';
import SVGImageFromPath from '../../../Components/SVGImageFromPath';
import {getStatusLabel, TenantStatus, tenantStatusMapToStatusChip} from '../tenants.utils';

const DISPLAY_WEBKIT_BOX = '-webkit-box';
const WORD_BREAK_WORD = 'break-word';
const GRID_TEMPLATE_COLUMNS = 'repeat(4, minmax(12.5rem, 1fr))';

export const sectionTitleLabelSx = {
  fontSize: '1.0rem',
  fontWeight: 700,
  color: 'body.900',
  marginBottom: '1.5rem',
  lineHeight: 1.25,
};

export const titleLabelSx = {
  fontSize: '0.75rem',
  color: 'body.500',
  fontWeight: 600,
};

export const valueLabelSx = {
  fontSize: '0.875rem',
  color: 'body.800',
  fontWeight: 600,
};

export const ellipsisValueLabelSx = {
  fontSize: '0.875rem',
  color: 'body.800',
  fontWeight: 600,
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  display: DISPLAY_WEBKIT_BOX,
  WebkitLineClamp: 1,
  WebkitBoxOrient: 'vertical',
  wordBreak: WORD_BREAK_WORD,
};

/**
 * Component to render loading state
 */
const LoadingState: React.FC = () => (
  <CenterLoaderContainer isLoading={true}>
    <Box></Box>
  </CenterLoaderContainer>
);

/**
 * Component to render error state
 */
const ErrorState: React.FC = () => (
  <Container maxWidth={false} sx={{maxWidth: '75rem', padding: '0.5rem', margin: '0 auto'}}>
    <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px'}}>
      <Typography color="error">Error loading tenant details. Please try again.</Typography>
    </Box>
  </Container>
);

/**
 * Component to render not found state
 */
const NotFoundState: React.FC = () => (
  <Container maxWidth={false} sx={{maxWidth: '75rem', padding: '0.5rem', margin: '0 auto'}}>
    <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '400px'}}>
      <Typography>Tenant not found.</Typography>
    </Box>
  </Container>
);

/**
 * TenantDetailsPage component - matches Figma design exactly
 * Displays comprehensive tenant information with proper styling
 */
const TenantDetailsPage: React.FC = () => {
  const {tenantId} = useParams<{tenantId: string}>();

  // Prepare subscription filter
  const subscriptionFilter = React.useMemo(
    () => ({
      where: {subscriberId: tenantId},
      include: [
        {
          relation: 'plan',
          scope: {
            include: [{relation: 'billingCycle'}, {relation: 'currency'}],
          },
        },
      ],
    }),
    [tenantId],
  );

  // Prepare billing filter
  const billingFilter = React.useMemo(
    () => ({
      tenantId: tenantId,
    }),
    [tenantId],
  );

  // Parallel queries
  const {
    data: tenant,
    isLoading: isTenantLoading,
    error: tenantError,
  } = useGetTenantByIdQuery(tenantId || '', {
    skip: !tenantId,
  });

  const {
    data: subscriptions,
    isLoading: isSubscriptionLoading,
    error: subscriptionError,
  } = useGetSubscriptionQuery(subscriptionFilter, {skip: !tenantId});

  const {
    data: tenantBillings,
    isLoading: isTenantBillingsLoading,
    error: tenantBillingsError,
  } = useGetTenantBillingsQuery(billingFilter, {skip: !tenantId});

  if (isTenantLoading || isSubscriptionLoading || isTenantBillingsLoading) return <LoadingState />;
  if (tenantError || subscriptionError || tenantBillingsError) return <ErrorState />;
  if (!tenant) return <NotFoundState />;

  const documentsData = tenant.files || [];

  return (
    <TenantDetailsContent
      tenant={tenant}
      tenantId={tenantId}
      documentsData={documentsData}
      subscriptions={subscriptions}
      tenantBillings={tenantBillings}
    />
  );
};

/**
 * Helper functions extracted from main component
 */
const formatDate = (dateString?: string) => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  } catch {
    return 'N/A';
  }
};

// Constants for phone number formatting
const PHONE_NUMBER_LENGTH = 10;
const AREA_CODE_START = 0;
const AREA_CODE_END = 3;
const EXCHANGE_START = 3;
const EXCHANGE_END = 6;
const SUBSCRIBER_START = 6;

const formatPhoneNumber = (countryCode?: string, phone?: string) => {
  if (!phone) return 'N/A';
  // Remove all non-digit characters
  const digits = phone.replace(/\D/g, '');

  const country = countryCode ? `${countryCode} ` : '';

  // Format as (XXX) XXX-XXXX for 10-digit numbers
  if (digits.length === PHONE_NUMBER_LENGTH) {
    return `${country}(${digits.slice(AREA_CODE_START, AREA_CODE_END)}) ${digits.slice(EXCHANGE_START, EXCHANGE_END)}-${digits.slice(SUBSCRIBER_START)}`;
  }

  // Return original if not 10 digits
  return `${country}${phone}`;
};

const capitalizeWords = (text?: string) => {
  if (!text) return 'N/A';
  return text
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

const getCurrencySymbol = (subscriptions?: Subscription[]) => {
  if (!subscriptions?.[0]?.plan?.currency?.symbol) {
    return '';
  }
  return subscriptions?.[0]?.plan?.currency?.symbol || '';
};

const getTotalCostForAllUsers = (subscriptions?: Subscription[]) => {
  if (subscriptions?.[0]?.plan?.allowedUnlimitedUsers) {
    return 'N/A';
  }
  const numberOfUsers = +(subscriptions?.[0]?.numberOfUsers ?? 0);
  const costPerUserRaw = subscriptions?.[0]?.plan?.costPerUser;
  const costPerUser = Number(costPerUserRaw);
  if (
    typeof numberOfUsers === 'number' &&
    !isNaN(costPerUser) &&
    costPerUserRaw !== undefined &&
    costPerUserRaw !== null
  ) {
    const totalCost = numberOfUsers * costPerUser;
    return `${getCurrencySymbol(subscriptions)}${totalCost.toFixed(2)}`;
  }
  return 'N/A';
};

const getContactFullName = (contact?: {firstName?: string; lastName?: string}) => {
  if (contact?.firstName && contact?.lastName) {
    return `${capitalizeWords(contact.firstName)} ${capitalizeWords(contact.lastName)}`;
  }
  return 'N/A';
};

const getTenantDisplayName = (tenantDetails?: {name?: string}) => tenantDetails?.name || 'Unnamed Tenant';

const renderBillingCreatedDate = (tenantBillings?: ITenantBillingInvoice[]) => {
  if (tenantBillings?.[0]?.createdOn) {
    return (
      <Tooltip title="Invoice Generated On">
        <span>{formatDate(tenantBillings[0].createdOn)}</span>
      </Tooltip>
    );
  }
  return '-';
};

/**
 * Main content component for tenant details
 */

interface TenantDetailsContentProps {
  tenant: TenantType;
  tenantId: string | undefined;
  documentsData: IFile[];
  subscriptions?: Subscription[];
  tenantBillings?: ITenantBillingInvoice[];
}

const TenantDetailsContent: React.FC<TenantDetailsContentProps> = ({
  tenant,
  tenantId,
  documentsData,
  subscriptions,
  tenantBillings,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const tenantName = getTenantDisplayName(tenant.tenantDetails);

  // Custom breadcrumb items for tenant details page
  const breadcrumbItems = [
    {label: 'Tenants', url: '/tenants'},
    {label: tenantName, url: `/tenants/${tenantId}`},
  ];

  const handleEdit = () => {
    navigate(`/tenants/${tenantId}/edit`);
  };

  const handleObservability = () => {
    //functionality to handle observability action
  };

  const handleClose = () => {
    //functionality to handle close action
    navigate('/tenants');
  };

  const handleDocumentClick = (file: IFile) => {
    // Open file in new tab for viewing (not downloading)
    if (file.url && file.url !== '#') {
      // If direct URL is available, open it in new tab
      window.open(file.url, '_blank', 'noopener,noreferrer'); // NOSONAR
    } else if (file.fileKey) {
      // If no direct URL but fileKey exists, construct the URL
      // You may need to adjust this based on your API structure
      const fileUrl = `/api/files/${file.fileKey}`;
      window.open(fileUrl, '_blank', 'noopener,noreferrer'); // NOSONAR
    } else {
      // Fallback: try to open using a constructed URL
      // This might need adjustment based on your backend API
      window.open(`/api/tenants/${file.tenantId}/files/${file.id}`, '_blank', 'noopener,noreferrer'); // NOSONAR
    }
  };

  const infraConfig = () => {
    const planTier = getPlanTierType(subscriptions?.[0]?.plan?.tier);
    return planTier ? (getCombinedPlanTierName(planTier) ?? '-') : '-';
  };
  const planStatus = subscriptions?.[0]?.plan?.status as unknown as PlanStatus;

  const isEditActionEnabled = [
    TenantStatus.PROVISIONING,
    TenantStatus.DEPROVISIONING,
    TenantStatus.SUSPENDING,
    TenantStatus.REACTIVATING,
    TenantStatus.MIGRATING,
    TenantStatus.PENDINGREACTIVATION,
    TenantStatus.INACTIVE,
    TenantStatus.PENDINGPROVISIONEXPIRED,
  ].includes(tenant.status);
  return (
    <Box>
      {/* Header Section */}
      <Box sx={{marginBottom: '1rem'}}>
        {/* Title, Status and Action Buttons Row */}
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-start',
            justifyContent: 'space-between',
            marginBottom: '0.5rem',
          }}
        >
          {/* Left side - Title, Status and Breadcrumb */}
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              gap: '0.5rem',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  fontSize: '1.125rem',
                  fontWeight: 600,
                  color: theme.palette.text.primary,
                  lineHeight: 1.2,
                }}
              >
                {tenantName}
              </Typography>
              <StatusChip
                label={getStatusLabel(tenant.tenantDetails.status as unknown as TenantStatus)}
                status={tenantStatusMapToStatusChip[tenant.tenantDetails.status as unknown as TenantStatus]}
              />
            </Box>
            <Breadcrumb items={breadcrumbItems} />
          </Box>

          {/* Right side - Action Buttons */}
          <Box
            sx={{
              display: 'flex',
              gap: '0.75rem',
              alignItems: 'flex-start',
              // marginTop: '0.25rem', // Slight adjustment to align with text baseline
            }}
          >
            <PermissionWrapper permission={PermissionKey.UpdateTenant}>
              <Button
                variant="outlined"
                startIcon={
                  <SVGImageFromPath
                    path={EditIcon}
                    sx={{
                      width: '1rem',
                      height: '1rem',
                      color: theme.palette.text.primary,
                      opacity: isEditActionEnabled ? 1 : 0.5,
                    }}
                  />
                }
                onClick={handleEdit}
                sx={{
                  borderRadius: '0.375rem',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  textTransform: 'none',
                  borderColor: theme.palette.grey[Integers.ThreeHundred],
                  color: theme.palette.text.primary,
                  '&:hover': {
                    borderColor: theme.palette.grey[Integers.FourHundred],
                    backgroundColor: theme.palette.grey[50],
                  },
                }}
                disabled={!isEditActionEnabled}
              >
                Edit
              </Button>
            </PermissionWrapper>

            <PermissionWrapper permission={PermissionKey.UpdateTenant}>
              <Button
                variant="outlined"
                startIcon={
                  <SVGImageFromPath
                    path={ObservabilityIcon}
                    sx={{
                      width: '1rem',
                      height: '1rem',
                      color: theme.palette.text.primary,
                    }}
                  />
                }
                onClick={handleObservability}
                sx={{
                  borderRadius: '0.375rem',
                  padding: '0.5rem 1rem',
                  fontSize: '0.875rem',
                  fontWeight: 500,
                  textTransform: 'none',
                  borderColor: theme.palette.grey[Integers.ThreeHundred],
                  color: theme.palette.text.primary,
                  '&:hover': {
                    borderColor: theme.palette.grey[Integers.FourHundred],
                    backgroundColor: theme.palette.grey[50],
                  },
                }}
              >
                Observability
              </Button>
            </PermissionWrapper>
          </Box>
        </Box>
      </Box>

      {/* Content Cards */}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: '1.5rem',
        }}
      >
        {/* Main Information Container - All sections in one card */}
        <Box
          sx={{
            backgroundColor: theme.palette.white.main,
            borderRadius: '0.75rem',
            padding: '1.5rem',
            border: `1px solid ${theme.palette.body[Integers.TwoHundred]}`,
          }}
        >
          {/* Tenant Information Section */}
          <Typography variant="h6" sx={sectionTitleLabelSx}>
            Tenant information
          </Typography>

          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: GRID_TEMPLATE_COLUMNS,
              gap: '2rem',
              marginBottom: '1.5rem',
            }}
          >
            <Box>
              <Typography sx={titleLabelSx}>Created date</Typography>
              <Typography sx={valueLabelSx}>{formatDate(tenant.tenantDetails.createdOn)}</Typography>
            </Box>

            <Box>
              <Typography sx={titleLabelSx}>Billing date</Typography>
              <Typography sx={valueLabelSx}>{renderBillingCreatedDate(tenantBillings)}</Typography>
            </Box>

            <Box>
              <Typography sx={titleLabelSx}>Subdomain</Typography>
              <EllipsisText
                text={tenant.tenantDetails.key?.toLowerCase().replace(/\s+/g, '-') ?? 'N/A'}
                sx={ellipsisValueLabelSx}
              />
            </Box>
          </Box>

          {/* Separator */}
          <Box
            sx={{
              height: '1px',
              backgroundColor: theme.palette.grey[Integers.TwoHundred],
              marginBottom: '2rem',
            }}
          />

          {/* Plan Details Section */}
          <Typography variant="h6" sx={sectionTitleLabelSx}>
            Plan details
          </Typography>

          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: GRID_TEMPLATE_COLUMNS,
              gap: '2rem',
              marginBottom: '1.5rem',
            }}
          >
            <Box>
              <Typography sx={titleLabelSx}>Plan name</Typography>
              <EllipsisText text={subscriptions?.[0]?.plan?.name ?? 'N/A'} sx={ellipsisValueLabelSx} />
            </Box>
            <Box>
              <Typography sx={titleLabelSx}>Billing cycle</Typography>
              <Typography sx={valueLabelSx}>{subscriptions?.[0]?.plan?.billingCycle?.cycleName ?? 'N/A'}</Typography>
            </Box>
            <Box>
              <Typography sx={titleLabelSx}>No. of users</Typography>
              <Typography sx={valueLabelSx}>
                {subscriptions?.[0]?.plan?.allowedUnlimitedUsers
                  ? 'Unlimited'
                  : (subscriptions?.[0]?.numberOfUsers ?? 'N/A')}
              </Typography>
            </Box>
            <Box>
              <Typography sx={titleLabelSx}>Total cost for all users</Typography>
              <Typography sx={valueLabelSx}>{getTotalCostForAllUsers(subscriptions)}</Typography>
            </Box>
            <Box>
              <Typography sx={titleLabelSx}>Plan cost</Typography>
              <Typography sx={valueLabelSx}>
                {`${getCurrencySymbol(subscriptions)}${subscriptions?.[0]?.plan?.price}`}
              </Typography>
            </Box>
            <Box>
              <Typography sx={titleLabelSx}>Total cost</Typography>
              <Typography sx={valueLabelSx}>
                {`${getCurrencySymbol(subscriptions)}${subscriptions?.[0]?.totalCost}`}
              </Typography>
            </Box>
            <Box>
              <Typography sx={titleLabelSx}>Status</Typography>
              <PlanStatusChip status={planStatus} />
            </Box>

            <Box>
              <Typography sx={titleLabelSx}>Infra configuration</Typography>
              <EllipsisText text={infraConfig()} sx={ellipsisValueLabelSx} />
            </Box>
          </Box>

          {/* Separator */}
          <Box
            sx={{
              height: '1px',
              backgroundColor: theme.palette.grey[Integers.TwoHundred],
              marginBottom: '2rem',
            }}
          />

          {/* Contact Information Section */}
          <Typography variant="h6" sx={sectionTitleLabelSx}>
            Contact information
          </Typography>

          {/* First Row */}
          <Box
            sx={{
              display: 'grid',
              gridTemplateColumns: GRID_TEMPLATE_COLUMNS,
              gap: '2rem',
              marginBottom: '1.5rem',
            }}
          >
            {/* Name */}
            <Box>
              <Typography sx={titleLabelSx}>Name</Typography>
              <EllipsisText text={getContactFullName(tenant.contact)} sx={ellipsisValueLabelSx} />
            </Box>

            {/* Job Title */}
            <Box>
              <Typography sx={titleLabelSx}>Job title</Typography>
              <EllipsisText text={tenant.contact?.designation ?? 'N/A'} sx={ellipsisValueLabelSx} />
            </Box>

            {/* Email */}
            <Box>
              <Typography sx={titleLabelSx}>Email</Typography>
              <EllipsisText text={tenant.contact?.email ?? 'N/A'} sx={ellipsisValueLabelSx} />
            </Box>

            {/* Phone Number */}
            <Box>
              <Typography sx={titleLabelSx}>Phone number</Typography>
              <EllipsisText
                text={formatPhoneNumber(tenant.contact?.countryCode, tenant.contact?.phoneNumber)}
                sx={ellipsisValueLabelSx}
              />
            </Box>

            <Box>
              <Typography sx={titleLabelSx}>City</Typography>
              <EllipsisText text={capitalizeWords(tenant.tenantDetails.address?.city)} sx={ellipsisValueLabelSx} />
            </Box>

            <Box>
              <Typography sx={titleLabelSx}>State</Typography>
              <EllipsisText text={capitalizeWords(tenant.tenantDetails.address?.state)} sx={ellipsisValueLabelSx} />
            </Box>
          </Box>

          {/* Separator */}
          <Box
            sx={{
              height: '1px',
              backgroundColor: theme.palette.grey[Integers.TwoHundred],
              marginBottom: '2rem',
            }}
          />

          {/* Documents Uploaded Section */}
          <Typography variant="h6" sx={sectionTitleLabelSx}>
            Documents uploaded
          </Typography>

          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: '1rem',
            }}
          >
            {documentsData.length > 0 ? (
              documentsData.map(doc => (
                <DocumentItem
                  key={doc.id}
                  file={
                    {
                      id: doc.id,
                      tenantId: doc.tenantId || tenantId || '',
                      fileKey: doc.fileKey,
                      originalName: doc.originalName,
                      source: doc.source || 1,
                      size: doc.size,
                      url: doc?.signedUrl || '#',
                    } as IFile
                  }
                  onClick={handleDocumentClick}
                />
              ))
            ) : (
              <Typography
                sx={{
                  fontSize: '0.875rem',
                  color: 'body.500',
                  fontStyle: 'italic',
                }}
              >
                No documents uploaded
              </Typography>
            )}
          </Box>
        </Box>
      </Box>

      {/* Close Button */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'flex-end',
          marginTop: '2rem',
        }}
      >
        <CloseButton onClick={handleClose} />
      </Box>
    </Box>
  );
};

export default TenantDetailsPage;
