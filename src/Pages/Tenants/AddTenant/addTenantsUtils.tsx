import {IFile} from 'redux/app/types';
import {PlanResponse} from 'redux/app/types/plan.type';
import * as yup from 'yup';
export const steps = ['Tenant Details', 'Plan Details', 'Documents'];

export const countryCodes = [{code: '+1', label: 'USA'}];

export type PlanSelectedType = {
  planId: string;
  name?: string;
  duration?: string;
  amount?: string | number;
  billingCycleId?: string;
};

export interface CountryCode {
  code: string;
  label: string;
}

// Regex constants
const COMPANY_NAME_REGEX = /^(?![-\s])[a-zA-Z0-9\s&.,'’-]+(?<![-\s])$/;
const SUBDOMAIN_REGEX = /^(?!-)[a-zA-Z0-9-]{3,63}(?<!-)$/;
const NAME_REGEX = /^[a-zA-Z\s]+$/;
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@([a-zA-Z0-9-]+\.){1,3}[a-zA-Z]{2,}$/;
const ALPHANUMERIC_REGEX = /^[a-zA-Z0-9]+$/;
const MOBILE_NUMBER_REGEX = /^\d{10}$/;
// Constants
const COMPANY_NAME_MIN = 1;
const COMPANY_NAME_MAX = 50;

const SUBDOMAIN_MIN = 3;
const SUBDOMAIN_MAX = 63;

const NAME_MIN = 1;
const USER_NAME_MIN = 3;
const NAME_MAX = 50;

const DESIGNATION_MIN = 3;
const DESIGNATION_MAX = 50;

const EMAIL_MAX = 254;

const MOBILE_NUMBER_LENGTH = 10;
const noWhiteSpace = 'not-only-whitespace';

export const addTenantValidationSchema = yup.object({
  company: yup
    .string()
    .required('Company is required')
    .min(COMPANY_NAME_MIN, `Company should have at least ${COMPANY_NAME_MIN} characters`)
    .max(COMPANY_NAME_MAX, `Company should have at most ${COMPANY_NAME_MAX} characters`)
    .matches(
      COMPANY_NAME_REGEX,
      'Company name should only contain letters, numbers, spaces, and valid punctuation in between.',
    )
    .test(noWhiteSpace, 'Company cannot be only whitespace', value => !!value?.trim()),

  key: yup
    .string()
    .required('Subdomain is required')
    .min(SUBDOMAIN_MIN, `Minimum ${SUBDOMAIN_MIN} characters required`)
    .max(SUBDOMAIN_MAX, `Maximum ${SUBDOMAIN_MAX} characters allowed`)
    .matches(SUBDOMAIN_REGEX, 'Subdomain should only contain lowercase letters, numbers, and hyphen in between.')
    .test(noWhiteSpace, 'Subdomain cannot be only whitespace', value => !!value?.trim()),

  firstName: yup
    .string()
    .required('First name is required')
    .min(NAME_MIN, `First name should have at least ${NAME_MIN} characters`)
    .max(NAME_MAX, `First name should have at most ${NAME_MAX} characters`)
    .matches(NAME_REGEX, 'First name should only contain letters')
    .test(noWhiteSpace, 'First name cannot be only whitespace', value => !!value?.trim()),

  userName: yup
    .string()
    .required('User name is required')
    .min(USER_NAME_MIN, `User name should have at least ${USER_NAME_MIN} characters`)
    .max(NAME_MAX, `User name should have at most ${NAME_MAX} characters`)
    .matches(/^[^\s]+$/, 'User name cannot contain spaces')
    .test(noWhiteSpace, 'User name cannot be only whitespace', value => !!value?.trim()),

  lastName: yup
    .string()
    .required('Last name is required')
    .min(NAME_MIN, `Last name should have at least ${NAME_MIN} characters`)
    .max(NAME_MAX, `Last name should have at most ${NAME_MAX} characters`)
    .matches(NAME_REGEX, 'Last name should only contain letters')
    .test(noWhiteSpace, 'Last name cannot be only whitespace', value => !!value?.trim()),

  designation: yup
    .string()
    .min(DESIGNATION_MIN, `Designation should have at least ${DESIGNATION_MIN} characters`)
    .max(DESIGNATION_MAX, `Designation should have at most ${DESIGNATION_MAX} characters`)
    .matches(NAME_REGEX, 'Designation should only contain letters')
    .test(noWhiteSpace, 'Designation cannot be only whitespace', value => value === undefined || !!value.trim()),

  email: yup
    .string()
    .required('Email is required')
    .email('Invalid email address')
    .max(EMAIL_MAX, `Email should have at most ${EMAIL_MAX} characters`)
    .matches(EMAIL_REGEX, 'Invalid email address')
    .test(noWhiteSpace, 'Email cannot be only whitespace', value => !!value?.trim()),

  countryCode: yup
    .object()
    .shape({
      value: yup.string(),
      label: yup.string(),
    })
    .required('Country code is required'),

  city: yup.string().required('City is required'),

  mobileNumber: yup
    .string()
    .matches(MOBILE_NUMBER_REGEX, `Mobile number must be exactly ${MOBILE_NUMBER_LENGTH} digits`)
    .test(noWhiteSpace, 'Mobile number cannot be only whitespace', value => value === undefined || !!value.trim()),

  state: yup.string().required('State is required'),
});

export interface FormAddTenant {
  id?: string;
  firstName: string;
  lastName: string;
  userName: string;
  company: string;
  designation: string | undefined;
  email: string;
  countryCode: CountryCode;
  mobileNumber: string | undefined;
  language: string;
  overAllPlan?: PlanResponse;
  userCount?: number;
  totalCost?: number;
  files: IFile[];
  key: string;
  city: string;
  state: string;
  leadId?: string;
  stateId?: string;
}

export interface ILeadInfo {
  leadId: string;
  firstName: string;
  lastName: string;
  companyName: string;
  email: string;
  designation: string;
  phoneNumber: string;
  countryCode: string;
  city: string;
  state: string;
  stateId?: string;
}

export const initialAddTenantValues = (leadInfo?: ILeadInfo): FormAddTenant => {
  return {
    firstName: leadInfo?.firstName ?? '',
    lastName: leadInfo?.lastName ?? '',
    company: leadInfo?.companyName ?? '',
    designation: leadInfo?.designation ?? undefined,
    email: leadInfo?.email ?? '',
    key: '',
    userName: '',
    countryCode: {code: '+1', label: 'USA'},
    mobileNumber: leadInfo?.phoneNumber ?? undefined,
    language: 'English',
    files: [],
    overAllPlan: undefined,
    userCount: 0,
    totalCost: 0,
    city: leadInfo?.city ?? '',
    state: leadInfo?.stateId ?? '',
    stateId: leadInfo?.state ?? '',
    leadId: leadInfo?.leadId ?? undefined,
  };
};

export enum TenantCreationStepType {
  TenantDetails = 0,
  PlanDetails = 1,
  Documents = 2,
}
