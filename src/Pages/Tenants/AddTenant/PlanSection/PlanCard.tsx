import {Box, Divider, Paper, Stack, Typography} from '@mui/material';
import Button from 'Components/Button';
import {Integers} from 'Helpers/integers';
import React from 'react';
import {PlanResponse} from 'redux/app/types/plan.type';
import {renderTier} from './ChoosePlan';

interface PlanCardProps {
  plan: PlanResponse;
  isSelected: boolean;
  onSelect: (id: string) => void;
  tenurePeriod: string;
  selectedGradient: string;
  gradient: string;
  userCostLabel: (p: PlanResponse) => string;
}

const whiteMain = 'white.main';

const cardHeaderStyle = {
  fontWeight: 700,
  fontSize: '0.875rem',
  lineHeight: '1.125rem',
  color: 'body.dark',
  mt: 1,
};

/**
 * Renders a card component displaying details of a subscription plan.
 *
 * @param plan - The plan object containing details such as name, price, currency, tier, and device configuration.
 * @param isSelected - Boolean indicating if the plan is currently selected.
 * @param onSelect - Callback function invoked when the plan is selected, receives the plan's id as an argument.
 * @param tenurePeriod - The tenure period string (e.g., "monthly", "yearly") used for display.
 * @param selectedGradient - The background gradient applied when the plan is selected.
 * @param gradient - The default background gradient for the plan card.
 * @param userCostLabel - Function that returns a string label for the user cost, given the plan object.
 *
 * @returns A styled card component displaying plan information, pricing, configuration, and selection controls.
 */
const PlanCard: React.FC<PlanCardProps> = ({
  plan,
  isSelected,
  onSelect,
  tenurePeriod,
  selectedGradient,
  gradient,
  userCostLabel,
}) => (
  <Box
    key={plan.id}
    data-testid="plan-card"
    sx={{
      scrollSnapAlign: 'start',
      flexShrink: 0,
      flexBasis: {xs: '88%', sm: '60%', md: '45%', lg: '33.33%', xl: '25%'},
      flexGrow: {xs: '0.88', sm: '0.60', md: '0.45', lg: '0.33', xl: '0.25'},
    }}
  >
    <Paper
      variant="outlined"
      sx={{
        height: '100%',
        borderRadius: 2,
        overflow: 'hidden',
        backgroundImage: isSelected ? selectedGradient : gradient,
        borderColor: isSelected ? 'primary.main' : 'divider',
        position: 'relative',
      }}
      aria-label={`${plan.name} plan`}
    >
      <Box sx={{p: 3}}>
        <Typography
          variant="h6"
          align="center"
          sx={{
            mb: 1,
            fontWeight: 700,
            fontSize: '0.875rem',
            lineHeight: '1.125rem',
            color: 'body.900',
          }}
        >
          {plan.name}
        </Typography>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'baseline',
            justifyContent: 'center',
            gap: 1,
            mb: 2,
          }}
        >
          <Typography
            variant="h3"
            component="span"
            sx={{fontWeight: 700, fontSize: '2.5rem', lineHeight: '2.875rem', color: 'body.900'}}
          >
            {plan.currency?.symbol}
            {plan.price}
          </Typography>
          <Typography
            variant="subtitle1"
            component="span"
            sx={{color: 'body.700', fontWeight: 500, fontSize: '0.875rem', lineHeight: '1.125rem'}}
          >
            /{tenurePeriod.slice(0, -2)}
          </Typography>
        </Box>
        {isSelected ? (
          <Button
            fullWidth
            variant="contained"
            size="large"
            sx={{
              borderRadius: 1,
              fontSize: '0.875rem',
              fontWeight: 700,
              color: 'body.dark',
              backgroundColor: whiteMain,
              border: theme => `0.0625rem solid ${theme.palette.body[Integers.NineHundred]}`,
              '&:hover': {background: whiteMain},
            }}
          >
            Selected Plan
          </Button>
        ) : (
          <Button
            data-testid="select-plan-btn"
            fullWidth
            variant="outlined"
            size="large"
            sx={{
              borderRadius: 1,
              fontWeight: 700,
              fontSize: '0.875rem',

              color: 'body.dark',
              borderWidth: '0.0625rem',
              borderStyle: 'solid',
              borderColor: 'body.200',
              backgroundColor: 'white.main',
              '&:hover': {background: whiteMain},
            }}
            onClick={() => onSelect(plan.id)}
          >
            Select This Plan
          </Button>
        )}
        <Stack spacing={2} sx={{mt: 3}}>
          <Box>
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                textAlign: 'center',
                fontSize: '0.6875rem',
                fontWeight: 500,
                lineHeight: '0.8125rem',
                color: 'body.800',
              }}
            >
              Software level
            </Typography>
            <Typography variant="subtitle2" align="center" sx={{...cardHeaderStyle, fontWeight: 0}}>
              <strong>{plan.softwareLevel ? plan.softwareLevel.level : '-'}</strong>
            </Typography>
          </Box>
          <Divider />
          <Box>
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                textAlign: 'center',
                fontSize: '0.6875rem',
                fontWeight: 500,
                lineHeight: '0.8125rem',
                color: 'body.800',
              }}
            >
              Infra configuration
            </Typography>
            <Typography variant="subtitle2" align="center" sx={{...cardHeaderStyle, fontWeight: 0}}>
              {renderTier(plan.tier)}
            </Typography>
          </Box>
          <Divider />
          <Box>
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                textAlign: 'center',
                fontSize: '0.6875rem',
                lineHeight: '0.8125rem',
                color: 'body.800',
              }}
            >
              No. of devices
            </Typography>
            <Typography variant="subtitle2" align="center" sx={cardHeaderStyle}>
              {plan.configureDevice?.min && plan.configureDevice?.max
                ? `${plan.configureDevice.min} - ${plan.configureDevice.max}`
                : '-'}
            </Typography>
          </Box>
          <Divider />
          <Box>
            <Typography
              variant="caption"
              sx={{
                display: 'block',
                textAlign: 'center',
                fontSize: '0.6875rem',
                lineHeight: '0.8125rem',
                color: 'body.800',
              }}
            >
              Users
            </Typography>
            <Typography variant="subtitle2" align="center" sx={cardHeaderStyle}>
              {userCostLabel(plan)}
            </Typography>
          </Box>
        </Stack>
      </Box>
    </Paper>
  </Box>
);

export default PlanCard;
