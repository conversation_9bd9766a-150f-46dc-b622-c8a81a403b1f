import {differenceInDays} from 'date-fns';
import {Subscription, SubscriptionStatus} from 'redux/app/types';
import {afterEach, beforeEach, describe, expect, it, vi} from 'vitest';
import {formatTrialPeriod, getTrialInfo} from './FreeTrialCard.utils';

// Mock date-fns
vi.mock('date-fns', () => ({
  differenceInDays: vi.fn(),
}));

const mockDifferenceInDays = vi.mocked(differenceInDays);

describe('FreeTrialCard.utils', () => {
  // Helper function to create mock subscription - moved to top level
  const createMockSubscription = (overrides: Partial<Subscription> = {}): Subscription => ({
    id: 'sub-123',
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '2025-01-01T00:00:00Z',
    modifiedOn: '2025-01-01T00:00:00Z',
    createdBy: 'user-123',
    modifiedBy: null,
    subscriberId: 'tenant-123',
    startDate: '2025-01-01T00:00:00Z',
    endDate: '2025-02-01T00:00:00Z',
    status: SubscriptionStatus.TRIAL,
    planId: 'plan-123',
    tierId: 'tier-123',
    clusterId: 'cluster-123',
    tenantId: 'tenant-123',
    plan: {} as any,
    numberOfUsers: 5,
    totalCost: 0,
    tier: {} as any,
    trialEndDate: '2025-01-30T00:00:00Z',
    tagId: 'tag-123',
    ...overrides,
  });

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock current date to be consistent across tests
    vi.useFakeTimers();
    vi.setSystemTime(new Date('2025-01-15T10:00:00Z'));
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('getTrialInfo', () => {
    it('returns null when subscription is null or undefined', () => {
      expect(getTrialInfo(null as any)).toBeNull();
      expect(getTrialInfo(undefined as any)).toBeNull();
    });

    it('returns null when createdOn is missing', () => {
      const subscription = createMockSubscription({
        createdOn: '',
        trialEndDate: '2025-01-30T00:00:00Z',
      });

      expect(getTrialInfo(subscription)).toBeNull();
    });

    it('returns null when trialEndDate is missing', () => {
      const subscription = createMockSubscription({
        createdOn: '2025-01-01T00:00:00Z',
        trialEndDate: undefined,
      });

      expect(getTrialInfo(subscription)).toBeNull();
    });

    it('returns null when both createdOn and trialEndDate are missing', () => {
      const subscription = createMockSubscription({
        createdOn: '',
        trialEndDate: undefined,
      });

      expect(getTrialInfo(subscription)).toBeNull();
    });

    it('calculates trial info correctly for active trial', () => {
      mockDifferenceInDays.mockReturnValue(15); // 15 days remaining

      const subscription = createMockSubscription({
        createdOn: '2025-01-01T00:00:00Z',
        trialEndDate: '2025-01-30T00:00:00Z',
        status: SubscriptionStatus.TRIAL,
      });

      const result = getTrialInfo(subscription);

      expect(result).toEqual({
        startDate: '2025-01-01T00:00:00Z',
        endDate: '2025-01-30T00:00:00Z',
        daysRemaining: 15,
        isExpired: false,
      });

      expect(mockDifferenceInDays).toHaveBeenCalledWith('2025-01-30T00:00:00Z', expect.any(Date));
    });

    it('sets daysRemaining to 0 when negative days calculated', () => {
      mockDifferenceInDays.mockReturnValue(-5); // Trial ended 5 days ago

      const subscription = createMockSubscription({
        createdOn: '2025-01-01T00:00:00Z',
        trialEndDate: '2025-01-10T00:00:00Z',
        status: SubscriptionStatus.TRIAL_EXPIRED,
      });

      const result = getTrialInfo(subscription);

      expect(result).toEqual({
        startDate: '2025-01-01T00:00:00Z',
        endDate: '2025-01-10T00:00:00Z',
        daysRemaining: 0,
        isExpired: true,
      });
    });

    it('marks trial as expired when status is TRIAL_SUSPEND', () => {
      mockDifferenceInDays.mockReturnValue(5);

      const subscription = createMockSubscription({
        status: SubscriptionStatus.TRIAL_SUSPEND,
      });

      const result = getTrialInfo(subscription);

      expect(result?.isExpired).toBe(true);
    });

    it('marks trial as expired when status is TRIAL_EXPIRED', () => {
      mockDifferenceInDays.mockReturnValue(0);

      const subscription = createMockSubscription({
        status: SubscriptionStatus.TRIAL_EXPIRED,
      });

      const result = getTrialInfo(subscription);

      expect(result?.isExpired).toBe(true);
    });

    it('marks trial as not expired for other statuses', () => {
      mockDifferenceInDays.mockReturnValue(10);

      const testStatuses = [
        SubscriptionStatus.ACTIVE,
        SubscriptionStatus.TRIAL,
        SubscriptionStatus.PENDING,
        SubscriptionStatus.INACTIVE,
        SubscriptionStatus.CANCELLED,
        SubscriptionStatus.EXPIRED,
        SubscriptionStatus.TRIAL_REMINDER_SENT,
      ];

      testStatuses.forEach(status => {
        const subscription = createMockSubscription({status});
        const result = getTrialInfo(subscription);
        expect(result?.isExpired).toBe(false);
      });
    });

    it('handles zero days remaining correctly', () => {
      mockDifferenceInDays.mockReturnValue(0);

      const subscription = createMockSubscription({
        status: SubscriptionStatus.TRIAL,
      });

      const result = getTrialInfo(subscription);

      expect(result?.daysRemaining).toBe(0);
      expect(result?.isExpired).toBe(false);
    });
  });

  describe('formatTrialPeriod', () => {
    beforeEach(() => {
      // Use real timers for date formatting tests
      vi.useRealTimers();
    });

    afterEach(() => {
      vi.useFakeTimers();
    });

    it('formats period with start and end date strings', () => {
      const result = formatTrialPeriod('2025-01-01', '2025-01-15');

      // Expected format: "1 Jan - 15 Jan 2025" (same year, so start year omitted)
      expect(result).toMatch(/^\d{1,2} \w{3} - \d{1,2} \w{3} \d{4}$/);
      expect(result).toContain('Jan');
      expect(result).toContain('2025');
    });

    it('formats period with start date and number of days', () => {
      const result = formatTrialPeriod('2025-01-01', 14);

      // Should calculate end date as 14 days after start date
      expect(result).toMatch(/^\d{1,2} \w{3} - \d{1,2} \w{3} \d{4}$/);
      expect(result).toContain('Jan');
      expect(result).toContain('2025');
    });

    it('includes year in start date when dates are in different years', () => {
      const result = formatTrialPeriod('2024-12-20', '2025-01-10');

      // Different years, so both should include year
      expect(result).toMatch(/^\d{1,2} \w{3} \d{4} - \d{1,2} \w{3} \d{4}$/);
      expect(result).toContain('2024');
      expect(result).toContain('2025');
    });

    it('omits year in start date when dates are in same year', () => {
      const result = formatTrialPeriod('2025-01-01', '2025-06-01');

      // Same year, so start date should not include year
      expect(result).toMatch(/^\d{1,2} \w{3} - \d{1,2} \w{3} \d{4}$/);
      expect(result).toContain('Jan');
      expect(result).toContain('Jun');
      expect(result).toContain('2025');
      // Should only have one occurrence of 2025 (in end date)
      expect((result.match(/2025/g) || []).length).toBe(1);
    });

    it('handles same start and end date', () => {
      const result = formatTrialPeriod('2025-01-01', '2025-01-01');

      expect(result).toMatch(/^\d{1,2} \w{3} - \d{1,2} \w{3} \d{4}$/);
      expect(result).toContain('Jan');
    });

    it('falls back to start date when no end date provided', () => {
      const result = formatTrialPeriod('2025-01-01');

      // Should use start date as end date
      expect(result).toMatch(/^\d{1,2} \w{3} - \d{1,2} \w{3} \d{4}$/);
      expect(result).toContain('Jan');
    });

    it('handles zero days correctly', () => {
      const result = formatTrialPeriod('2025-01-01', 0);

      // Zero days should result in same date
      expect(result).toMatch(/^\d{1,2} \w{3} - \d{1,2} \w{3} \d{4}$/);
      expect(result).toContain('Jan');
    });

    it('handles negative days by going backwards', () => {
      const result = formatTrialPeriod('2025-01-15', -5);

      // Should go 5 days back from Jan 15
      expect(result).toMatch(/^\d{1,2} \w{3} - \d{1,2} \w{3} \d{4}$/);
      expect(result).toContain('Jan');
    });

    it('handles large number of days spanning multiple months', () => {
      const result = formatTrialPeriod('2025-01-01', 100);

      // 100 days from Jan 1 should go into April
      expect(result).toMatch(/^\d{1,2} \w{3} - \d{1,2} \w{3} \d{4}$/);
      expect(result).toContain('Jan');
      expect(result).toContain('Apr');
    });

    it('uses en-GB locale formatting', () => {
      const result = formatTrialPeriod('2025-01-01', '2025-02-01');

      // Should use British date format (day before month)
      expect(result).toMatch(/^1 Jan - 1 Feb 2025$/);
    });
  });

  describe('Edge Cases', () => {
    describe('getTrialInfo edge cases', () => {
      it('handles subscription with null values', () => {
        const subscription = createMockSubscription({
          createdOn: null as any,
          trialEndDate: null as any,
        });

        expect(getTrialInfo(subscription)).toBeNull();
      });

      it('handles subscription with empty string dates', () => {
        const subscription = createMockSubscription({
          createdOn: '',
          trialEndDate: '',
        });

        expect(getTrialInfo(subscription)).toBeNull();
      });

      it('handles very large positive days remaining', () => {
        mockDifferenceInDays.mockReturnValue(999999);

        const subscription = createMockSubscription({
          status: SubscriptionStatus.TRIAL,
        });

        const result = getTrialInfo(subscription);
        expect(result?.daysRemaining).toBe(999999);
      });

      it('handles very large negative days remaining', () => {
        mockDifferenceInDays.mockReturnValue(-999999);

        const subscription = createMockSubscription({
          status: SubscriptionStatus.TRIAL_EXPIRED,
        });

        const result = getTrialInfo(subscription);
        expect(result?.daysRemaining).toBe(0);
        expect(result?.isExpired).toBe(true);
      });
    });

    describe('formatTrialPeriod edge cases', () => {
      beforeEach(() => {
        vi.useRealTimers();
      });

      afterEach(() => {
        vi.useFakeTimers();
      });

      it('handles invalid date strings gracefully', () => {
        // Invalid date should still produce some output
        const result = formatTrialPeriod('invalid-date', 'another-invalid-date');
        expect(typeof result).toBe('string');
        expect(result).toContain('-');
      });

      it('handles very large number of days', () => {
        const result = formatTrialPeriod('2025-01-01', 10000);
        expect(typeof result).toBe('string');
        expect(result).toContain('-');
        // For very large spans, the format might include year in start date
        expect(result).toMatch(/^\d{1,2} \w{3}( \d{4})? - \d{1,2} \w{3} \d{4}$/);
      });

      it('handles leap year calculations', () => {
        // Test with leap year date
        const result = formatTrialPeriod('2024-02-28', 2);
        expect(result).toContain('Feb');
        expect(result).toContain('Mar');
      });

      it('handles month boundaries correctly', () => {
        const result = formatTrialPeriod('2025-01-31', 1);
        expect(result).toContain('Jan');
        expect(result).toContain('Feb');
      });

      it('handles year boundaries correctly', () => {
        const result = formatTrialPeriod('2024-12-31', 1);
        expect(result).toContain('Dec');
        expect(result).toContain('Jan');
        expect(result).toContain('2024');
        expect(result).toContain('2025');
      });
    });
  });

  describe('Real-world scenarios', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    it('handles typical 14-day trial scenario', () => {
      vi.setSystemTime(new Date('2025-01-08T10:00:00Z')); // 7 days into trial
      mockDifferenceInDays.mockReturnValue(7); // 7 days remaining

      const subscription = createMockSubscription({
        createdOn: '2025-01-01T00:00:00Z',
        trialEndDate: '2025-01-15T00:00:00Z',
        status: SubscriptionStatus.TRIAL,
      });

      const trialInfo = getTrialInfo(subscription);
      expect(trialInfo).toEqual({
        startDate: '2025-01-01T00:00:00Z',
        endDate: '2025-01-15T00:00:00Z',
        daysRemaining: 7,
        isExpired: false,
      });

      vi.useRealTimers();
      const formatted = formatTrialPeriod(trialInfo!.startDate, trialInfo!.endDate);
      expect(formatted).toMatch(/^1 Jan - 15 Jan 2025$/);
      vi.useFakeTimers();
    });

    it('handles trial that just expired', () => {
      vi.setSystemTime(new Date('2025-01-16T10:00:00Z')); // 1 day after trial end
      mockDifferenceInDays.mockReturnValue(-1); // 1 day past expiry

      const subscription = createMockSubscription({
        createdOn: '2025-01-01T00:00:00Z',
        trialEndDate: '2025-01-15T00:00:00Z',
        status: SubscriptionStatus.TRIAL_EXPIRED,
      });

      const trialInfo = getTrialInfo(subscription);
      expect(trialInfo).toEqual({
        startDate: '2025-01-01T00:00:00Z',
        endDate: '2025-01-15T00:00:00Z',
        daysRemaining: 0,
        isExpired: true,
      });
    });

    it('handles trial suspended mid-way', () => {
      vi.setSystemTime(new Date('2025-01-08T10:00:00Z'));
      mockDifferenceInDays.mockReturnValue(7); // Would have 7 days left

      const subscription = createMockSubscription({
        createdOn: '2025-01-01T00:00:00Z',
        trialEndDate: '2025-01-15T00:00:00Z',
        status: SubscriptionStatus.TRIAL_SUSPEND,
      });

      const trialInfo = getTrialInfo(subscription);
      expect(trialInfo).toEqual({
        startDate: '2025-01-01T00:00:00Z',
        endDate: '2025-01-15T00:00:00Z',
        daysRemaining: 7,
        isExpired: true, // Marked as expired due to suspension
      });
    });
  });

  describe('Integration tests', () => {
    it('works together - getTrialInfo and formatTrialPeriod', () => {
      vi.useFakeTimers();
      vi.setSystemTime(new Date('2025-01-15T10:00:00Z'));

      mockDifferenceInDays.mockReturnValue(10);

      const subscription: Subscription = {
        id: 'sub-123',
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '2025-01-01T00:00:00Z',
        modifiedOn: '2025-01-01T00:00:00Z',
        createdBy: 'user-123',
        modifiedBy: null,
        subscriberId: 'tenant-123',
        startDate: '2025-01-01T00:00:00Z',
        endDate: '2025-02-01T00:00:00Z',
        status: SubscriptionStatus.TRIAL,
        planId: 'plan-123',
        tierId: 'tier-123',
        clusterId: 'cluster-123',
        tenantId: 'tenant-123',
        plan: {} as any,
        numberOfUsers: 5,
        totalCost: 0,
        tier: {} as any,
        trialEndDate: '2025-01-25T00:00:00Z',
        tagId: 'tag-123',
      };

      const trialInfo = getTrialInfo(subscription);
      expect(trialInfo).not.toBeNull();

      if (trialInfo) {
        vi.useRealTimers();
        const formattedPeriod = formatTrialPeriod(trialInfo.startDate, trialInfo.endDate);
        expect(formattedPeriod).toMatch(/^\d{1,2} \w{3} - \d{1,2} \w{3} \d{4}$/);
        expect(formattedPeriod).toContain('Jan');
      }

      vi.useFakeTimers();
    });

    it('handles complete trial lifecycle', () => {
      vi.useFakeTimers();

      // Test different stages of trial lifecycle
      const baseSubscription = createMockSubscription({
        createdOn: '2025-01-01T00:00:00Z',
        trialEndDate: '2025-01-15T00:00:00Z',
      });

      // Day 1: Trial just started
      vi.setSystemTime(new Date('2025-01-01T10:00:00Z'));
      mockDifferenceInDays.mockReturnValue(14);
      let trialInfo = getTrialInfo({...baseSubscription, status: SubscriptionStatus.TRIAL});
      expect(trialInfo?.daysRemaining).toBe(14);
      expect(trialInfo?.isExpired).toBe(false);

      // Day 8: Mid-trial
      vi.setSystemTime(new Date('2025-01-08T10:00:00Z'));
      mockDifferenceInDays.mockReturnValue(7);
      trialInfo = getTrialInfo({...baseSubscription, status: SubscriptionStatus.TRIAL});
      expect(trialInfo?.daysRemaining).toBe(7);
      expect(trialInfo?.isExpired).toBe(false);

      // Day 15: Last day
      vi.setSystemTime(new Date('2025-01-15T10:00:00Z'));
      mockDifferenceInDays.mockReturnValue(0);
      trialInfo = getTrialInfo({...baseSubscription, status: SubscriptionStatus.TRIAL});
      expect(trialInfo?.daysRemaining).toBe(0);
      expect(trialInfo?.isExpired).toBe(false);

      // Day 16: Expired
      vi.setSystemTime(new Date('2025-01-16T10:00:00Z'));
      mockDifferenceInDays.mockReturnValue(-1);
      trialInfo = getTrialInfo({...baseSubscription, status: SubscriptionStatus.TRIAL_EXPIRED});
      expect(trialInfo?.daysRemaining).toBe(0);
      expect(trialInfo?.isExpired).toBe(true);
    });
  });
});
