import {render, renderHook, screen} from '@testing-library/react';
import {PlanResponse} from 'redux/app/types/plan.type';
import {vi} from 'vitest';
import {PlanSelectedType} from '../addTenantsUtils';
import {useAddTenantSteps} from './useAddTenantSteps';

vi.mock('Components/UploadDocument', () => ({
  default: ({primaryText}: {primaryText: string}) => <div>{primaryText}</div>,
}));

vi.mock('../AddTenantDetail', () => ({
  default: () => <div>AddTenantDetails Component</div>,
}));

vi.mock('../PlanSection/ChoosePlan', () => ({
  default: ({overAllPlan}: {overAllPlan: PlanSelectedType}) => (
    <div>ChoosePlan Component - {overAllPlan?.name || 'No Plan'}</div>
  ),
}));

describe('useAddTenantSteps hook', () => {
  const handleNextButton = vi.fn();
  const setOverAllPlan = vi.fn();
  const files = [new File(['test'], 'test.txt')];
  const onFileUpload = vi.fn();
  const onRemoveFile = vi.fn();
  const overAllPlan = {name: 'Premium', id: 'p1', price: 100} as unknown as PlanResponse;
  const userCount = 0;
  const setUserCount = vi.fn();
  const selectedDevice = 'Device';
  const setSelectedDevice = vi.fn();
  const selectedTenure = 'Tenure';
  const setSelectedTenure = vi.fn();
  const selectedInfraConfig = 'Infra';
  const setSelectedInfraConfig = vi.fn();

  const props = {
    handleNextButton,
    setOverAllPlan,
    files,
    onFileUpload,
    onRemoveFile,
    overAllPlan,
    userCount,
    setUserCount,
    selectedDevice,
    setSelectedDevice,
    selectedTenure,
    setSelectedTenure,
    selectedInfraConfig,
    setSelectedInfraConfig,
    triggerValidateUser: vi.fn(),
  };

  it('should return correct component for step 0', () => {
    const {result} = renderHook(() => useAddTenantSteps(props));
    const Component = result.current.renderStepContent(0);
    render(<>{Component}</>);
    expect(screen.getByText('AddTenantDetails Component')).toBeInTheDocument();
  });

  it('should return correct component for step 1 with overAllPlan', () => {
    const {result} = renderHook(() => useAddTenantSteps(props));
    const Component = result.current.renderStepContent(1);
    render(<>{Component}</>);
    expect(screen.getByText(/ChoosePlan Component/)).toHaveTextContent('Premium');
  });

  it('should return correct component for step 2 with UploadDocuments', () => {
    const {result} = renderHook(() => useAddTenantSteps(props));
    const Component = result.current.renderStepContent(2);
    render(<>{Component}</>);
    expect(screen.getByText('0 Files attached')).toBeInTheDocument();
  });
});
