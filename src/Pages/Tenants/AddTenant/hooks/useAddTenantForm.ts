import {FetchBaseQueryError} from '@reduxjs/toolkit/query';
import {FormActions} from 'Components/Forms/Form/FormUtils';
import {getErrorMessage} from 'Helpers/utils';
import {isEqual} from 'lodash';
import {enqueueSnackbar} from 'notistack';
import {PlanStatus} from 'Pages/PlansPage/plans.utils';
import {useState} from 'react';
import {useCreateTenantMutation, useUpdateTenantByIdMutation} from 'redux/app/tenantManagementApiSlice';
import {IFile} from 'types';
import {FormAddTenant, ILeadInfo, initialAddTenantValues, TenantCreationStepType} from '../addTenantsUtils';

/**
 * Props for the `useAddTenantForm` hook.
 */
interface UseAddTenantFormProps {
  /** Files selected for upload */
  files: File[];
  /** Callback to toggle dialog visibility */
  setDialogOpen: (open: boolean) => void;
  /** Callback to set the active step in tenant creation */
  setActiveStep: (step: TenantCreationStepType) => void;
  /** Callback to store the created tenant name */
  setCreatedTenant: (name: string | undefined) => void;
  /** Whether the form is in edit mode */
  isEdit?: boolean;
  /** Previously uploaded files associated with the tenant */
  existingFiles?: IFile[];
  /** Tenant ID (used in edit mode) */
  tenantId?: string;
  /** Values injected for editing an existing tenant */
  injectedValues?: FormAddTenant;
  leadInfo?: ILeadInfo;
  trialSelected?: boolean;
}

/**
 * Return type of the `useAddTenantForm` hook.
 */
interface UseAddTenantFormReturn {
  /** Initial values for the form */
  initialValues: FormAddTenant;
  /** Handler to submit the form */
  formSubmit: (values: FormAddTenant, actions: FormActions<FormAddTenant>) => Promise<void>;
  /** Indicates whether a create or update operation is in progress */
  isLoading: boolean;
}

/**
 * Custom hook to handle add/edit tenant form logic.
 *
 * - Supports tenant creation and update.
 * - Handles validation of changes before updating.
 * - Builds `FormData` for API submission.
 *
 * @param props - Hook props to configure tenant creation or editing
 * @returns Object containing initial values, submit handler, and loading state
 */
export const useAddTenantForm = ({
  files,
  setDialogOpen,
  setActiveStep,
  setCreatedTenant,
  isEdit,
  existingFiles,
  tenantId,
  injectedValues,
  leadInfo,
  trialSelected,
}: UseAddTenantFormProps): UseAddTenantFormReturn => {
  const [initialValues, setInitialValues] = useState<FormAddTenant>(initialAddTenantValues(leadInfo));

  const [createTenant, {isLoading}] = useCreateTenantMutation();
  const [updateTenant, {isLoading: updateTenantLoading}] = useUpdateTenantByIdMutation();

  /**
   * Handles form submission for tenant creation or update.
   *
   * @param values - Form values
   * @param actions - Formik form actions
   */
  const formSubmit = async (values: FormAddTenant, actions: FormActions<FormAddTenant>): Promise<void> => {
    if (isEdit && injectedValues) {
      const isUpdateRequired = checkIfUpdateRequired(values, injectedValues, existingFiles, files);
      if (!isUpdateRequired) {
        enqueueSnackbar('No changes detected.', {variant: 'info'});
        return;
      }
    }

    const formData = createFormData(values, files, isEdit ?? false, existingFiles ?? undefined, trialSelected);

    try {
      await saveTenant(formData, values);
      setInitialValues(initialAddTenantValues());
      actions.resetForm({values: initialValues});
      setDialogOpen(true);
    } catch (error) {
      handleFormError(error, actions, setActiveStep);
    }
  };

  /**
   * Checks whether updates are required by comparing current values
   * with injected values (edit mode).
   */
  function checkIfUpdateRequired(
    values: FormAddTenant,
    injectedValues: FormAddTenant,
    existingFiles: IFile[] | undefined,
    files: File[],
  ): boolean {
    return [
      hasPlanChanged(values, injectedValues),
      hasUserCountChanged(values, injectedValues),
      values.totalCost !== injectedValues.totalCost,
      hasContactChanged(values, injectedValues),
      hasAddressChanged(values, injectedValues),
      !isEqual(existingFiles, injectedValues.files),
      files.length > 0,
    ].some(Boolean);
  }

  /** Checks if the selected plan has changed */
  function hasPlanChanged(values: FormAddTenant, injected: FormAddTenant): boolean {
    return !isEqual(values.overAllPlan, injected.overAllPlan);
  }

  /** Checks if user count has changed */
  function hasUserCountChanged(values: FormAddTenant, injected: FormAddTenant): boolean {
    let injectedUserCount = injected.userCount === 0 ? 1 : injected.userCount;
    if (injectedValues?.overAllPlan?.status == PlanStatus.TRIAL.toString()) injectedUserCount = 0;
    return Number(values.userCount) !== Number(injectedUserCount);
  }

  /** Checks if contact details have changed */
  function hasContactChanged(values: FormAddTenant, injected: FormAddTenant): boolean {
    return (
      values.firstName !== injected.firstName ||
      values.lastName !== injected.lastName ||
      values.userName !== injected.userName ||
      values.email !== injected.email ||
      values.designation !== injected.designation ||
      values.mobileNumber !== injected.mobileNumber
    );
  }

  /** Checks if address details have changed */
  function hasAddressChanged(values: FormAddTenant, injected: FormAddTenant): boolean {
    return values.city !== injected.city || values.state !== injected.state;
  }

  /**
   * Saves a tenant by calling the API (create or update).
   *
   * @param formData - Data to send
   * @param values - Original form values
   */
  async function saveTenant(formData: FormData, values: FormAddTenant) {
    if (isEdit && tenantId) {
      await updateTenant({id: tenantId, body: formData}).unwrap();
    } else {
      await createTenant(formData).unwrap();
    }
    setCreatedTenant(values.company);
  }

  return {
    initialValues,
    formSubmit,
    isLoading: isLoading || updateTenantLoading,
  };
};

const appendIfDefined = (formData: FormData, key: string, value?: string | null) => {
  if (value !== undefined && value !== null && value !== '') {
    formData.append(key, String(value));
  }
};

/**
 * Creates a `FormData` object from tenant form values and uploaded files.
 *
 * @param values - Tenant form values
 * @param files - Files to upload
 * @param isEdit - Whether in edit mode
 * @param existingFiles - Files already stored
 * @returns Constructed FormData for submission
 */
const createFormData = (
  values: FormAddTenant,
  files: File[],
  isEdit?: boolean,
  existingFiles?: IFile[],
  trialSelected?: boolean,
): FormData => {
  const formData = new FormData();
  formData.append('name', values.company.trim());
  formData.append('lang', values.language.trim());

  if (values.overAllPlan) {
    formData.append('planId', values.overAllPlan.id);
    if (values.userCount !== undefined && !values.overAllPlan.allowedUnlimitedUsers) {
      formData.append('numberOfUsers', values.userCount.toString());
    }
    if (values.totalCost !== undefined) {
      formData.append('totalCost', values.totalCost.toString());
    }
  }

  if (trialSelected) {
    formData.append('isTrialApplied', 'true');
  }
  formData.append(
    'contact',
    JSON.stringify({
      firstName: values.firstName.trim(),
      lastName: values.lastName.trim(),
      isPrimary: true,
      email: values.email.trim(),
      phoneNumber: values.mobileNumber?.trim(),
      userName: values.userName.trim(),
      countryCode: values.countryCode.code.trim(),
      designation: values.designation?.trim(),
    }),
  );

  files.forEach(file => {
    formData.append('files', file);
  });

  if (isEdit && existingFiles) {
    formData.append('existingFiles', JSON.stringify(existingFiles));
  }
  const country = 'USA';
  formData.append('key', values.key.trim());
  appendIfDefined(formData, 'city', values.city);
  appendIfDefined(formData, 'state', values.stateId);
  formData.append('country', country);
  if (values.leadId) {
    formData.append('leadId', values.leadId);
  }
  return formData;
};

/**
 * Handles form submission errors and updates form state accordingly.
 *
 * @param error - Error thrown during API call
 * @param actions - Formik actions to set form errors
 * @param setActiveStep - Callback to set the form step in case of validation errors
 */
const handleFormError = (
  error: unknown,
  actions: FormActions<FormAddTenant>,
  setActiveStep: (step: TenantCreationStepType) => void,
): void => {
  const errorMessage = getErrorMessage(error as FetchBaseQueryError);

  if (errorMessage.includes('Subdomain')) {
    setActiveStep(0);
    actions.setErrors({key: errorMessage});
  } else if (errorMessage.includes('email')) {
    setActiveStep(0);
    actions.setErrors({email: errorMessage});
  } else if (errorMessage.includes('Duplicate feature')) {
    setActiveStep(1);
  } else {
    // Do nothing
  }
};
