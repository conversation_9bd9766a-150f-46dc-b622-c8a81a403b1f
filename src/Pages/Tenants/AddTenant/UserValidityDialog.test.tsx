import {fireEvent, screen} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import {UserValidityDialog} from './UserValidityDialog';

// Mock the icon components
vi.mock('Assets/InfoCircle', () => ({
  default: (props: any) => <div data-testid="convert-check-icon" {...props} />,
}));

vi.mock('Assets/InvalidIcon', () => ({
  default: (props: any) => <div data-testid="invalid-icon" {...props} />,
}));

describe('UserValidityDialog', () => {
  const defaultProps = {
    open: true,
    actionType: 'convert',
    onClose: vi.fn(),
    onConfirm: vi.fn(),
    title: '<EMAIL> - testuser',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders dialog with correct title', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} />);

      expect(screen.getByText('User Validity')).toBeInTheDocument();
    });

    it('renders the action message', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} />);

      expect(
        screen.getByText(
          'This username/email combination already exists in Scada System. So we are auto filling the username/email for you.',
        ),
      ).toBeInTheDocument();
    });

    it('renders the title prop as subtitle', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} />);

      expect(screen.getByText('<EMAIL> - testuser')).toBeInTheDocument();
    });

    it('renders Cancel and Continue buttons', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} />);

      expect(screen.getByRole('button', {name: 'Cancel'})).toBeInTheDocument();
      expect(screen.getByRole('button', {name: 'Continue'})).toBeInTheDocument();
    });

    it('renders Continue button with correct test id', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} />);

      expect(screen.getByTestId('dialog-invalid-button')).toBeInTheDocument();
    });
  });

  describe('Icon Display', () => {
    it('displays ConvertCheckIcon when actionType is "convert"', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} actionType="convert" />);

      expect(screen.getByTestId('convert-check-icon')).toBeInTheDocument();
      expect(screen.queryByTestId('invalid-icon')).not.toBeInTheDocument();
    });

    it('displays InvalidIcon when actionType is not "convert"', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} actionType="error" />);

      expect(screen.getByTestId('invalid-icon')).toBeInTheDocument();
      expect(screen.queryByTestId('convert-check-icon')).not.toBeInTheDocument();
    });

    it('displays InvalidIcon when actionType is empty string', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} actionType="" />);

      expect(screen.getByTestId('invalid-icon')).toBeInTheDocument();
      expect(screen.queryByTestId('convert-check-icon')).not.toBeInTheDocument();
    });

    it('displays InvalidIcon when actionType is undefined', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} actionType={undefined as any} />);

      expect(screen.getByTestId('invalid-icon')).toBeInTheDocument();
      expect(screen.queryByTestId('convert-check-icon')).not.toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('calls onClose when Cancel button is clicked', () => {
      const onCloseMock = vi.fn();
      renderWithTheme(<UserValidityDialog {...defaultProps} onClose={onCloseMock} />);

      fireEvent.click(screen.getByRole('button', {name: 'Cancel'}));

      expect(onCloseMock).toHaveBeenCalledTimes(1);
    });

    it('calls onConfirm when Continue button is clicked', () => {
      const onConfirmMock = vi.fn();
      renderWithTheme(<UserValidityDialog {...defaultProps} onConfirm={onConfirmMock} />);

      fireEvent.click(screen.getByRole('button', {name: 'Continue'}));

      expect(onConfirmMock).toHaveBeenCalledTimes(1);
    });

    it('calls onClose when dialog close button is clicked', () => {
      const onCloseMock = vi.fn();
      renderWithTheme(<UserValidityDialog {...defaultProps} onClose={onCloseMock} />);

      const closeButton = screen.getByTestId('close-button');
      fireEvent.click(closeButton);

      expect(onCloseMock).toHaveBeenCalledTimes(1);
    });

    it('does not call handlers when buttons are clicked multiple times rapidly', () => {
      const onCloseMock = vi.fn();
      const onConfirmMock = vi.fn();
      renderWithTheme(<UserValidityDialog {...defaultProps} onClose={onCloseMock} onConfirm={onConfirmMock} />);

      const cancelButton = screen.getByRole('button', {name: 'Cancel'});
      const continueButton = screen.getByRole('button', {name: 'Continue'});

      // Rapid clicks
      fireEvent.click(cancelButton);
      fireEvent.click(cancelButton);
      fireEvent.click(continueButton);
      fireEvent.click(continueButton);

      expect(onCloseMock).toHaveBeenCalledTimes(2);
      expect(onConfirmMock).toHaveBeenCalledTimes(2);
    });
  });

  describe('Dialog State', () => {
    it('does not render when open is false', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} open={false} />);

      expect(screen.queryByText('User Validity')).not.toBeInTheDocument();
    });

    it('renders when open is true', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} open={true} />);

      expect(screen.getByText('User Validity')).toBeInTheDocument();
    });
  });

  describe('Props Variations', () => {
    it('handles empty title prop', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} title="" />);

      expect(screen.getByText('User Validity')).toBeInTheDocument();
      // The empty title should still render but be empty
      const titleElements = screen.getAllByText('');
      expect(titleElements.length).toBeGreaterThan(0);
    });

    it('handles long title prop', () => {
      const longTitle =
        '<EMAIL> - very_long_username_that_might_cause_layout_issues';
      renderWithTheme(<UserValidityDialog {...defaultProps} title={longTitle} />);

      expect(screen.getByText(longTitle)).toBeInTheDocument();
    });

    it('handles special characters in title', () => {
      const specialTitle = '<EMAIL> - user_name-123';
      renderWithTheme(<UserValidityDialog {...defaultProps} title={specialTitle} />);

      expect(screen.getByText(specialTitle)).toBeInTheDocument();
    });

    it('handles different actionType values', () => {
      const actionTypes = ['convert', 'error', 'invalid', 'warning', 'success', ''];

      actionTypes.forEach(actionType => {
        const {unmount} = renderWithTheme(<UserValidityDialog {...defaultProps} actionType={actionType} />);

        if (actionType === 'convert') {
          expect(screen.getByTestId('convert-check-icon')).toBeInTheDocument();
        } else {
          expect(screen.getByTestId('invalid-icon')).toBeInTheDocument();
        }

        unmount();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper button roles', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} />);

      const buttons = screen.getAllByRole('button');
      expect(buttons).toHaveLength(2); // Cancel and Continue buttons (close is a Box, not button)
    });

    it('has accessible button text', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} />);

      expect(screen.getByRole('button', {name: 'Cancel'})).toBeInTheDocument();
      expect(screen.getByRole('button', {name: 'Continue'})).toBeInTheDocument();
    });

    it('has close button with test id', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} />);

      expect(screen.getByTestId('close-button')).toBeInTheDocument();
    });
  });

  describe('Integration Scenarios', () => {
    it('handles convert action scenario', () => {
      const onConfirmMock = vi.fn();
      renderWithTheme(
        <UserValidityDialog
          {...defaultProps}
          actionType="convert"
          onConfirm={onConfirmMock}
          title="<EMAIL> - johndoe"
        />,
      );

      expect(screen.getByTestId('convert-check-icon')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL> - johndoe')).toBeInTheDocument();

      fireEvent.click(screen.getByRole('button', {name: 'Continue'}));
      expect(onConfirmMock).toHaveBeenCalled();
    });

    it('handles error action scenario', () => {
      const onCloseMock = vi.fn();
      renderWithTheme(
        <UserValidityDialog
          {...defaultProps}
          actionType="error"
          onClose={onCloseMock}
          title="<EMAIL> - invaliduser"
        />,
      );

      expect(screen.getByTestId('invalid-icon')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL> - invaliduser')).toBeInTheDocument();

      fireEvent.click(screen.getByRole('button', {name: 'Cancel'}));
      expect(onCloseMock).toHaveBeenCalled();
    });

    it('handles dialog workflow from open to close', () => {
      const onCloseMock = vi.fn();

      // Test with dialog open
      renderWithTheme(<UserValidityDialog {...defaultProps} open={true} onClose={onCloseMock} />);
      expect(screen.getByText('User Validity')).toBeInTheDocument();

      // Close dialog
      fireEvent.click(screen.getByRole('button', {name: 'Cancel'}));
      expect(onCloseMock).toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('handles null/undefined callbacks gracefully', () => {
      // This should not throw errors
      expect(() => {
        renderWithTheme(
          <UserValidityDialog {...defaultProps} onClose={undefined as any} onConfirm={undefined as any} />,
        );
      }).not.toThrow();
    });

    it('handles closed dialog state', () => {
      renderWithTheme(<UserValidityDialog {...defaultProps} open={false} />);

      expect(screen.queryByText('User Validity')).not.toBeInTheDocument();
    });

    it('handles different actionType values correctly', () => {
      // Test convert action
      const {unmount} = renderWithTheme(<UserValidityDialog {...defaultProps} actionType="convert" />);
      expect(screen.getByTestId('convert-check-icon')).toBeInTheDocument();
      unmount();

      // Test error action
      renderWithTheme(<UserValidityDialog {...defaultProps} actionType="error" />);
      expect(screen.getByTestId('invalid-icon')).toBeInTheDocument();
    });
  });
});
