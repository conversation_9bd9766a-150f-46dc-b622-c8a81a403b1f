/**
 * This component renders the HTML/JSX content for the tenant detail form.
 * It contains only the presentation layer, separated from the business logic.
 * Note: This component must be rendered within a Formik context for FormInput components to work properly.
 */

import {Box, Grid, InputAdornment, List, ListItem, ListItemButton, MenuItem, Select, Typography} from '@mui/material';
import FormInput from 'Components/Forms/FormInput';
import FormSelect from 'Components/Forms/FormSelect/FormSelect';
import {REGISTERED_DOMAIN} from 'Constants/enums';
import {Integers} from 'Helpers/integers';
import {StyleUtils} from 'Helpers/styleUtils';
import {useFormikContext} from 'formik';
import {UserValidityDialog} from './UserValidityDialog';
import {FormAddTenant} from './addTenantsUtils';

interface AddTenantDetailContentProps {
  values: FormAddTenant;
  editModeStyles: object;
  isEdit?: boolean;
  sxPropsValue: object;
  adornmentContent: React.ReactNode;
  isAvailable: boolean | null;
  suggestions: string[];
  setFieldValue: (field: string, value: unknown, shouldValidate?: boolean) => void;
  shouldDisable: boolean | undefined;
  stateOptions: {value: string; label: string}[];
  countryCodes: {code: string}[];
  renderNoItem: (stateSelected: boolean) => () => React.ReactNode;
  openDialog: boolean;
  handleValidityDialog: () => void;
  handleDialogClose: () => void;
}

/**
 * Renders the content for adding or editing tenant details, including company information,
 * subdomain selection with suggestions, and contact information fields.
 *
 * @component
 * @param {AddTenantDetailContentProps} props - The props for the component.
 * @param {any} props.values - The current form values.
 * @param {SxProps} props.editModeStyles - Styles applied when in edit mode.
 * @param {boolean} props.isEdit - Indicates if the form is in edit mode.
 * @param {SxProps} props.sxPropsValue - Additional style props for the subdomain input.
 * @param {React.ReactNode} props.adornmentContent - Content to display as an adornment in the subdomain input.
 * @param {boolean | undefined} props.isAvailable - Indicates if the subdomain is available.
 * @param {string[]} props.suggestions - List of suggested subdomains if the chosen one is unavailable.
 * @param {(field: string, value: any) => void} props.setFieldValue - Callback to set a form field value.
 * @param {boolean} props.shouldDisable - Whether certain fields should be disabled/read-only.
 * @param {Array<{ label: string; value: string }>} props.stateOptions - Options for the state select input.
 * @param {Array<{ label: string; value: string }>} props.cityOptions - Options for the city select input.
 * @param {Array<{ code: string }>} props.countryCodes - List of country codes for the phone number input.
 * @param {(hasState: boolean) => React.ReactNode} props.renderNoItem - Renderer for when no city options are available.
 * @param {boolean} props.openDialog - Whether the user validity dialog is open.
 * @param {() => void} props.handleValidityDialog - Handler for confirming the validity dialog.
 * @param {() => void} props.handleDialogClose - Handler for closing the validity dialog.
 *
 * @returns {JSX.Element} The rendered tenant detail content form.
 */
const AddTenantDetailContent: React.FC<AddTenantDetailContentProps> = ({
  values,
  editModeStyles,
  isEdit,
  sxPropsValue,
  adornmentContent,
  isAvailable,
  suggestions,
  setFieldValue,
  shouldDisable,
  stateOptions,
  countryCodes,
  renderNoItem,
  openDialog,
  handleValidityDialog,
  handleDialogClose,
}) => {
  // Ensure this component is rendered within Formik context for FormInput error display
  useFormikContext<FormAddTenant>();

  return (
    <Grid container spacing={2} rowSpacing={2} sx={{height: '100%', boxSizing: 'border-box', padding: 2}}>
      {/* Section Header */}
      <Grid size={{xs: 12}}>
        <Typography variant="h6" fontWeight={600} sx={{fontSize: '1rem'}}>
          Tenant Information
        </Typography>
      </Grid>

      {/* Company Name */}
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Company *</Typography>
        <FormInput
          fullWidth
          id="company"
          name="company"
          required
          sx={{...StyleUtils.inputBoxStyles, ...editModeStyles}}
          readOnly={isEdit}
          placeholder="Enter company name"
        />
      </Grid>

      {/* Subdomain Key */}
      <Grid container spacing={0} size={{xs: 12, sm: 6}} alignItems="stretch">
        <Grid size={{xs: 9}}>
          <Typography sx={StyleUtils.lalelStyles}>Subdomain *</Typography>
          <Box sx={{position: 'relative', display: 'inline-block', width: '100%'}}>
            <FormInput
              fullWidth
              id="key"
              name="key"
              required
              sx={[{...StyleUtils.inputBoxStyles, ...editModeStyles}, sxPropsValue]}
              placeholder="Enter subdomain"
              readOnly={isEdit}
              endAdornment={
                adornmentContent && (
                  <InputAdornment position="end" sx={{pr: 1}}>
                    {adornmentContent}
                  </InputAdornment>
                )
              }
            />

            {/* Subdomain suggestions */}
            {isAvailable === false && (
              <Box
                sx={{
                  border: theme => `0.0625rem solid ${theme.palette.body[Integers.OneHundred]}`,
                  mt: 1,
                  borderRadius: 1,
                  backgroundColor: 'white.main',
                  position: 'absolute',
                  zIndex: 2,
                  width: '100%',
                }}
              >
                <Typography sx={{fontSize: '0.85rem', p: 1}}>
                  <Typography component="span" sx={{color: 'alert.error.main', fontSize: '0.85rem'}}>
                    <strong>&quot;{values.key}&quot; is already taken.</strong>
                  </Typography>
                  <br />
                  Here are some suggestions.
                </Typography>

                {suggestions.length > 0 && (
                  <Grid container justifyContent="center" alignItems="center">
                    {suggestions.map(suggestion => (
                      <Grid size={{xs: 12}} key={suggestion} sx={{display: 'flex', justifyContent: 'center'}}>
                        <List dense sx={{width: '100%', display: 'flex', justifyContent: 'center', p: 0, m: 0}}>
                          <ListItem
                            disablePadding
                            sx={{
                              backgroundColor: 'white.200',
                              borderRadius: 2,
                              width: '96%',
                              mb: 1,
                            }}
                          >
                            <ListItemButton
                              data-testid={`suggestion-${suggestion}`}
                              onClick={() => setFieldValue('key', suggestion)}
                              sx={{fontSize: '0.9rem'}}
                            >
                              {suggestion + REGISTERED_DOMAIN}
                            </ListItemButton>
                          </ListItem>
                        </List>
                      </Grid>
                    ))}
                  </Grid>
                )}
              </Box>
            )}
          </Box>
        </Grid>

        {/* Domain suffix */}
        <Grid size={{xs: 3}}>
          <Box sx={{height: '100%', display: 'flex', paddingTop: '1.2rem', alignItems: 'center', pl: 0.5}}>
            <Typography>{REGISTERED_DOMAIN}</Typography>
          </Box>
        </Grid>
      </Grid>

      {/* Contact Info Header */}
      <Grid size={{xs: 12}} sx={{mt: 2}}>
        <Typography variant="h6" fontWeight={600} sx={{fontSize: '1rem'}}>
          Contact Information
        </Typography>
      </Grid>

      {/* Contact Fields */}
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>First name *</Typography>
        <FormInput
          fullWidth
          id="firstName"
          name="firstName"
          placeholder="Enter first name"
          required
          sx={{...StyleUtils.inputBoxStyles}}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Last name *</Typography>
        <FormInput
          fullWidth
          id="lastName"
          name="lastName"
          placeholder="Enter last name"
          required
          sx={{...StyleUtils.inputBoxStyles}}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>User name *</Typography>
        <FormInput
          fullWidth
          id="userName"
          name="userName"
          placeholder="Enter username"
          required
          readOnly={shouldDisable}
          sx={{
            ...StyleUtils.inputBoxStyles,
            ...(shouldDisable ? editModeStyles : {}),
          }}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Email address *</Typography>
        <FormInput
          fullWidth
          id="email"
          name="email"
          type="email"
          placeholder="Enter email address"
          required
          readOnly={shouldDisable}
          sx={{
            ...StyleUtils.inputBoxStyles,
            ...(shouldDisable ? editModeStyles : {}),
          }}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Job title</Typography>
        <FormInput
          fullWidth
          id="designation"
          name="designation"
          placeholder="Enter job title"
          sx={{...StyleUtils.inputBoxStyles}}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Primary phone number</Typography>
        <FormInput
          fullWidth
          id="mobileNumber"
          name="mobileNumber"
          placeholder="Enter primary phone number"
          onInput={e => {
            const target = e.target as HTMLInputElement;
            target.value = target.value.replaceAll(/\D/g, '');
          }}
          sxProps={{paddingLeft: 0}}
          sx={{...StyleUtils.inputBoxStyles, p: 0}}
          startAdornment={
            <InputAdornment position="start" sx={{...StyleUtils.inputAdornment}}>
              <Select
                data-testid="country-code-select"
                variant="standard"
                disableUnderline
                disabled
                defaultValue={values?.countryCode?.code || '+1'}
                sx={{
                  ...StyleUtils.selectBox,
                  '& .Mui-disabled': {
                    '-webkit-text-fill-color': 'black.main',
                  },
                }}
                IconComponent={() => null}
              >
                {countryCodes.map(option => (
                  <MenuItem key={option.code} value={option.code} data-testid={`country-code-${option.code}`}>
                    {option.code}
                  </MenuItem>
                ))}
              </Select>
            </InputAdornment>
          }
        />
      </Grid>

      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>State *</Typography>
        <FormSelect
          fullWidth
          id="state"
          name="state"
          placeholder="Select state"
          required
          sx={{...StyleUtils.selectBoxStyles}}
          options={stateOptions}
          menuPlacement="top"
          placeholderSx={{color: 'body.300'}}
          onChange={value => {
            const selected = stateOptions.find(opt => opt.value === value);

            setFieldValue('state', value);
            setFieldValue('stateId', selected ? selected.label : '');
          }}
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>City *</Typography>
        <FormInput fullWidth id="city" name="city" placeholder="City" required sx={{...StyleUtils.selectBoxStyles}} />
      </Grid>
      {openDialog && (
        <UserValidityDialog
          onConfirm={() => handleValidityDialog()}
          actionType={'convert'}
          open={openDialog}
          onClose={handleDialogClose}
          title={values.userName + ' - ' + values.email}
        />
      )}
    </Grid>
  );
};

export default AddTenantDetailContent;
