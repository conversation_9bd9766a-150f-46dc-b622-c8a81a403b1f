/// <reference types="vitest/globals" />
import '@testing-library/jest-dom';
import {fireEvent, render, screen} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import {Mock, vi} from 'vitest';
import AddTenantPage from './AddTenantPage';

// Robust theme mock for MUI useTheme
import {ThemeProvider, createTheme} from '@mui/material/styles';
const themeMock = createTheme({
  palette: {
    white: {main: '#fff'},
    primary: {main: '#1976d2'},
    error: {main: '#d32f2f'},
    warning: {main: '#ed6c02'},
    info: {main: '#0288d1'},
    success: {main: '#2e7d32'},
    grey: {50: '#f9fafb', 200: '#e5e7eb', 300: '#d1d5db', 400: '#9ca3af', 500: '#9e9e9e'},
    body: {900: '#222', 800: '#333', 700: '#444', 500: '#888', 200: '#bbb', 100: '#ccc', dark: '#000'},
    common: {black: '#000', white: '#fff'},
    background: {default: '#fafafa', paper: '#fff'},
    text: {primary: '#111827', secondary: '#6b7280'},
    divider: '#e0e0e0',
  },
});
vi.mock('@mui/material/styles', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('@mui/material', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('src/config/theme', () => ({
  default: themeMock,
}));
vi.mock('src/Providers/theme/default', () => ({
  default: themeMock,
}));

// Mock Child Components

// Define prop types
type BreadcrumbProps = {items: unknown[]};
type FormProps = {children: ReactNode};
type StepperTabProps = {activeStep: number};
type SuccessDialogProps = {isDialogOpen: boolean};
type RenderButtonProps = {handleCancel: () => void};

// Mocks
vi.mock('Components/Breadcrumb/Breadcrumb', () => ({
  default: ({items}: BreadcrumbProps) => <div data-testid="breadcrumb">{items?.length} breadcrumbs</div>,
}));

vi.mock('Components/Forms/Form', () => ({
  default: ({children}: FormProps) => <form data-testid="form">{children}</form>,
}));

vi.mock('Components/StepperTab/StepperTab', () => ({
  default: ({activeStep}: StepperTabProps) => <div data-testid="stepper">Step {activeStep}</div>,
}));

vi.mock('./PlanSection/SuccessDialog', () => ({
  default: ({isDialogOpen}: SuccessDialogProps) =>
    isDialogOpen ? <div data-testid="success-dialog">Dialog Open</div> : null,
}));

vi.mock('./RenderButton', () => ({
  default: ({handleCancel}: RenderButtonProps) => (
    <button data-testid="cancel-btn" onClick={handleCancel}>
      Cancel
    </button>
  ),
}));

// Mock Redux API hooks
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useLazyValidateUserQuery: () => [
    vi.fn().mockImplementation(params => {
      if (params.email === '<EMAIL>') {
        return Promise.reject('Email exists');
      }
      return Promise.resolve();
    }),
    {data: null, error: null, isFetching: false},
  ],
}));

// Mock Hooks
vi.mock('./hooks/useAddTenantState', () => ({
  useAddTenantState: vi.fn(),
}));
vi.mock('./hooks/useAddTenantForm', () => ({
  useAddTenantForm: vi.fn(),
}));
vi.mock('./hooks/useAddTenantSteps', () => ({
  useAddTenantSteps: vi.fn(),
}));

// Import mocked hooks after mocking them
import {ReactNode} from 'react';
import {useAddTenantForm} from './hooks/useAddTenantForm';
import {useAddTenantState} from './hooks/useAddTenantState';
import {useAddTenantSteps} from './hooks/useAddTenantSteps';

// Mock navigate
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

describe('AddTenantPage Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default Mock Implementation
    (useAddTenantState as Mock).mockReturnValue({
      activeStep: 0,
      handleNext: vi.fn(),
      handleBack: vi.fn(),
      handleNextButton: vi.fn(),
      files: [],
      cluster: null,
      overAllPlan: null,
      userCount: 1,
      setUserCount: vi.fn(),
      selectedDevice: undefined,
      setSelectedDevice: vi.fn(),
      selectedTenure: undefined,
      setSelectedTenure: vi.fn(),
      selectedInfraConfig: undefined,
      setSelectedInfraConfig: vi.fn(),
      isDialogOpen: false,
      setFiles: vi.fn(),
      setCluster: vi.fn(),
      setOverAllPlan: vi.fn(),
      onNavigateToTenant: vi.fn(),
      setIsDialogOpen: vi.fn(),
      setActiveStep: vi.fn(),
      nextButtonState: true,
      createdTenant: undefined,
      setCreatedTenant: vi.fn(),
      existingFiles: [],
      setExistingFiles: vi.fn(),
      trialSelected: false,
      setTrialSelected: vi.fn(),
    });

    (useAddTenantForm as unknown as Mock).mockReturnValue({
      initialValues: {name: ''},
      formSubmit: vi.fn(),
      isLoading: false,
    });

    (useAddTenantSteps as Mock).mockReturnValue({
      renderStepContent: vi.fn().mockReturnValue(<div data-testid="step-content">Step Content</div>),
      stepRefs: {},
    });
  });

  it('renders all main sections correctly', () => {
    render(
      <ThemeProvider theme={themeMock}>
        <MemoryRouter>
          <AddTenantPage />
        </MemoryRouter>
      </ThemeProvider>,
    );

    expect(screen.getByTestId('AddTenantPage')).toBeInTheDocument();
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument();
    expect(screen.getByTestId('stepper')).toHaveTextContent('Step 0');
    expect(screen.getByTestId('form')).toBeInTheDocument();
    expect(screen.getByTestId('step-content')).toHaveTextContent('Step Content');
  });

  it('opens success dialog when isDialogOpen is true', () => {
    (useAddTenantState as unknown as Mock).mockReturnValueOnce({
      ...useAddTenantState(),
      isDialogOpen: true,
    });

    render(
      <ThemeProvider theme={themeMock}>
        <MemoryRouter>
          <AddTenantPage />
        </MemoryRouter>
      </ThemeProvider>,
    );

    expect(screen.getByTestId('success-dialog')).toHaveTextContent('Dialog Open');
  });

  it('navigates back to tenants when cancel button is clicked', () => {
    render(
      <ThemeProvider theme={themeMock}>
        <MemoryRouter>
          <AddTenantPage />
        </MemoryRouter>
      </ThemeProvider>,
    );

    fireEvent.click(screen.getByTestId('cancel-btn'));
    expect(mockNavigate).toHaveBeenCalledWith('/tenants');
  });

  it('renders with uploaded files', () => {
    (useAddTenantState as unknown as Mock).mockReturnValueOnce({
      ...useAddTenantState(),
      files: [new File(['content'], 'test.pdf', {type: 'application/pdf'})],
    });

    render(
      <ThemeProvider theme={themeMock}>
        <MemoryRouter>
          <AddTenantPage />
        </MemoryRouter>
      </ThemeProvider>,
    );

    expect(screen.getByTestId('form')).toBeInTheDocument();
  });
});
