/**
 * This component renders the tenant detail form used for onboarding or editing a tenant.
 * It includes validation for subdomain availability and suggestions if the subdomain is taken.
 *
 * Features:
 * - Debounced subdomain availability check
 * - Dynamic adornment icons
 * - Read-only toggle for edit mode
 * - Company and contact detail input fields
 */

import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import {CircularProgress} from '@mui/material';
import {useFormikContext} from 'formik';
import {Integers} from 'Helpers/integers';
import {debounce} from 'lodash';
import NoItemRenderer from 'Pages/PendingTenants/NoItemRenderer';
import {forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState} from 'react';
import {GetState} from 'react-country-state-city';
import {useVerifyTenantKeyMutation} from 'redux/app/tenantManagementApiSlice';
import {AnyObject} from 'yup';
import Icon from '../../../Assets/Icons.svg';
import {TenantStatus} from '../tenants.utils';
import AddTenantDetailContent from './AddTenantDetailContent';
import {addTenantValidationSchema, countryCodes, FormAddTenant, steps} from './addTenantsUtils';

const alertSuccessMain = 'alert.success.onBg';
/**
 * API response type for verifying tenant key availability
 */
export type VerifyTenantKeyResponse = {
  available: boolean;
  suggestions?: string[];
};
const DEBOUNCE_DELAY_MS = 500;
interface Props {
  /** When true, disables editing of fields */
  isEdit?: boolean;
  tenantStatus?: number;
  /**
   * Function to trigger user validation.
   * Should match the signature returned by useLazyValidateUserQuery.
   */
  triggerValidateUser: (args: {email: string; userName: string; tenantId: string}) => Promise<AnyObject>;
}
// Error type for validateUserCombo catch block
interface ValidateUserError {
  status?: number;
  data?: {
    error?: {
      details?: {
        username?: string;
        email?: string;
      };
    };
  };
}

/**
 * Returns an adornment component (spinner/icon/cancel) for subdomain input
 */
const getEndAdornmentContent = ({
  key,
  loading,
  isAvailable,
  setFieldValue,
  setIsAvailable,
  setSuggestions,
  isKeyFieldValid,
  isEdit = false,
}: {
  key: string;
  loading: boolean;
  isAvailable: boolean | null;
  setFieldValue: (field: string, value: unknown, shouldValidate?: boolean) => void;
  setIsAvailable: (val: boolean | null) => void;
  setSuggestions: (val: string[]) => void;
  isKeyFieldValid?: boolean;
  isEdit?: boolean;
}) => {
  if (isEdit) {
    // If in edit mode, we might want to show different validation states
    return <img src={Icon} alt="Available" style={{width: 20, height: 20, objectFit: 'contain'}} />;
  }
  if ((key || '').length < steps.length) return null;

  if (loading && isKeyFieldValid) {
    return <CircularProgress size={20} />;
  }

  if (isAvailable === true && isKeyFieldValid) {
    return <img src={Icon} alt="Available" style={{width: 20, height: 20, objectFit: 'contain'}} />;
  }

  if (isAvailable === false && isKeyFieldValid) {
    return (
      <CancelOutlinedIcon
        color="error"
        sx={{cursor: 'pointer'}}
        onClick={() => {
          setFieldValue('key', '');
          setIsAvailable(null);
          setSuggestions([]);
        }}
      />
    );
  }

  return null;
};

type CustomNoItemRendererProps = {
  stateSelected: boolean;
};

export const CustomNoItemRenderer: React.FC<CustomNoItemRendererProps> = ({stateSelected}) => (
  <NoItemRenderer stateSelected={stateSelected} />
);

const renderNoItem = (stateSelected: boolean) => () => <CustomNoItemRenderer stateSelected={stateSelected} />;

/**
 * Returns MUI styles for the subdomain input box based on availability
 */
const getSxPropsValue = (isAvailable: boolean | null, keyLength: number, isKeyFieldValid?: boolean): AnyObject => {
  if ((isAvailable === true && keyLength < steps.length) || !isKeyFieldValid) {
    return {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: 'body.100',
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: 'body.100',
      },
    };
  }

  if (isAvailable === true) {
    return {
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: alertSuccessMain,
      },
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: alertSuccessMain,
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: alertSuccessMain,
      },
    };
  }

  if (isAvailable === false) {
    return {
      '&:hover .MuiOutlinedInput-notchedOutline': {
        borderColor: 'alert.error.onBg',
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderColor: 'alert.error.onBg',
      },
    };
  }

  return {};
};

type Mode = 'edit' | 'view' | 'create';

function getStyles(mode: Mode) {
  const styles: Record<Mode, object> = {
    edit: {
      backgroundColor: '#E9E9F1',
      '& .MuiOutlinedInput-notchedOutline': {
        borderColor: '#C0CEEA',
      },
      color: '#607294',
    },
    view: {},
    create: {},
  };

  return styles[mode];
}

/**
 * A form component for entering tenant company and contact information.
 * Supports both add and edit modes.
 */

const AddTenantDetail = forwardRef<{validateUserCombo: () => Promise<boolean>}, Props>(
  ({isEdit, tenantStatus, triggerValidateUser}, ref) => {
    const {values, errors, setFieldValue, validateForm, setFieldError, setValues} = useFormikContext<FormAddTenant>();
    const [openDialog, setOpenDialog] = useState(false);
    const [userComboError, setUserComboError] = useState<string | null>(null);

    // Sync userComboError to Formik values for cross-component access
    useEffect(() => {
      setFieldValue('userComboError', userComboError, false);
    }, [userComboError, setFieldValue]);
    const editModeStyles = isEdit ? getStyles('edit') : {};
    const [stateOptions, setStateOptions] = useState<{value: string; label: string}[]>([]);

    const [verifyTenantKey] = useVerifyTenantKeyMutation();
    const [isAvailable, setIsAvailable] = useState<boolean | null>(null);
    const [suggestions, setSuggestions] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);

    const shouldDisable = tenantStatus !== TenantStatus.ACTIVE && isEdit;
    /**
     * Debounced API call to verify tenant key availability
     */
    const debouncedVerify = useCallback(
      debounce(async (key: string, companyName: string) => {
        if (!key.trim()) return;
        setLoading(true);
        try {
          const res: VerifyTenantKeyResponse = await verifyTenantKey({key, companyName}).unwrap();
          setIsAvailable(res.available);
          setSuggestions(res.available ? [] : res.suggestions || []);
        } catch {
          setIsAvailable(false);
        } finally {
          setLoading(false);
        }
      }, DEBOUNCE_DELAY_MS),
      [],
    );

    const handleDialogClose = () => {
      setOpenDialog(false);
      if (dialogResolveRef.current) {
        dialogResolveRef.current(false);
        dialogResolveRef.current = null;
      }
    };

    const dialogResolveRef = useRef<((result: boolean) => void) | null>(null);

    const handleValidityDialog = () => {
      setOpenDialog(false);
      if (dialogResolveRef.current) {
        dialogResolveRef.current(true);
        dialogResolveRef.current = null;
      }
    };

    // Expose validateUserCombo method to parent via ref
    useImperativeHandle(ref, () => ({
      async validateUserCombo() {
        if (!isEdit || shouldDisable) return true;
        try {
          // If triggerValidateUser returns a promise, do not call unwrap
          const res = await triggerValidateUser({
            email: values.email,
            userName: values.userName,
            tenantId: values.id ?? '',
          });
          // If the API returns an error in the response, handle it here
          if (res?.error) {
            // Simulate throwing to enter catch block for unified error handling
            throw res.error;
          }
          if (res?.message === 'User already exists' && res?.data) {
            setUserComboError(null);
            setValues(prev => ({
              ...prev,
              userName: res.data.username ?? prev.userName,
              email: res.data.email ?? prev.email,
            }));
            return true;
          } else {
            setUserComboError(null);
            return true;
          }
        } catch (error: unknown) {
          return handleValidationError(error as ValidateUserError);
        }
      },
    }));

    function handleValidationError(err: ValidateUserError): boolean | Promise<boolean> {
      const errorStatusCode = err?.status;
      const userDetails = err.data?.error?.details;

      if (errorStatusCode === Integers.FourHundredTen) {
        setFieldError('userName', 'Username is already taken');
        setFieldValue('isUserNameValid', false, false);
        return false;
      }

      if (userDetails) {
        setValues(prev => ({
          ...prev,
          userName: userDetails.username ?? prev.userName,
          email: userDetails.email ?? prev.email,
        }));
        setOpenDialog(true);

        return new Promise<boolean>(resolve => {
          dialogResolveRef.current = resolve;
        });
      }

      return false;
    }

    /**
     * Trigger subdomain check when value changes and not in edit mode
     */
    useEffect(() => {
      if (values.key && !isEdit) {
        // Validate subdomain with Yup before calling debouncedVerify
        addTenantValidationSchema
          .validateAt('key', {key: values.key})
          .then(() => {
            debouncedVerify(values.key, values.company);
          })
          .catch(() => {
            // Do not call debouncedVerify if validation fails
          });
      }
    }, [values.key, debouncedVerify, isEdit]);

    const USA_ID = 233;

    useEffect(() => {
      const loadStates = async () => {
        try {
          const states = await GetState(USA_ID);
          setStateOptions(
            (states || [])
              .filter(state => state.hasCities) // keep only entries marked as true
              .map(state => ({
                value: state.id?.toString() || state.name,
                label: state.name,
              })),
          );
        } catch {
          setStateOptions([]);
        }
      };
      loadStates();
    }, []);

    // To check if the key field has an error AND has been touched
    const isKeyFieldValid = !errors.key;
    const sxPropsValue = getSxPropsValue(isAvailable, (values.key || '').length, isKeyFieldValid);
    const adornmentContent = getEndAdornmentContent({
      key: values.key,
      loading,
      isAvailable,
      setFieldValue,
      setIsAvailable,
      setSuggestions,
      isKeyFieldValid,
      isEdit,
    });

    // Validate form when city changes
    useEffect(() => {
      validateForm();
    }, [values.city]);

    return (
      <AddTenantDetailContent
        values={values}
        editModeStyles={editModeStyles}
        isEdit={isEdit}
        sxPropsValue={sxPropsValue}
        adornmentContent={adornmentContent}
        isAvailable={isAvailable}
        suggestions={suggestions}
        setFieldValue={setFieldValue}
        shouldDisable={shouldDisable}
        stateOptions={stateOptions}
        countryCodes={countryCodes}
        renderNoItem={renderNoItem}
        openDialog={openDialog}
        handleValidityDialog={handleValidityDialog}
        handleDialogClose={handleDialogClose}
      />
    );
  },
);

export default AddTenantDetail;
