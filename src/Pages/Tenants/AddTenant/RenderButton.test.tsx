import '@testing-library/jest-dom';
import {fireEvent, render, screen} from '@testing-library/react';
import {Mock, vi} from 'vitest';
import RenderButton from './RenderButton';

// Mock Formik Context
vi.mock('formik', () => ({
  useFormikContext: vi.fn(),
}));

const nextButtonTestId = 'next-button';

import {useFormikContext} from 'formik';

describe('RenderButton Component', () => {
  const mockHandleBack = vi.fn();
  const mockHandleNext = vi.fn();
  const mockHandleCancel = vi.fn();
  const mockHandleSubmit = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useFormikContext as Mock).mockReturnValue({
      isValid: true,
      dirty: true,
      handleSubmit: mockHandleSubmit,
      values: {
        company: 'Test Company',
        key: 'test-key',
        firstName: 'John',
        lastName: 'Doe',
        userName: 'johndoe',
        email: '<EMAIL>',
        city: 'Test City',
        state: 'CA',
        isUserNameValid: true,
        userComboError: null,
        overAllPlan: {id: 'plan-1'},
      },
      touched: {
        company: true,
        key: true,
        firstName: true,
        lastName: true,
        userName: true,
        email: true,
        city: true,
        state: true,
      },
    });
  });

  it('renders Next button for step 0 and triggers handleNext', () => {
    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={0}
        isEdit={true} // Set isEdit to true to bypass verifyApiCheck
      />,
    );

    const nextButton = screen.getByTestId(nextButtonTestId);
    expect(nextButton).toHaveTextContent('Plan Details >');
    expect(nextButton).not.toBeDisabled();
    fireEvent.click(nextButton);
    expect(mockHandleNext).toHaveBeenCalled();
  });

  it('renders Back button for step 1 and triggers handleBack', () => {
    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={1}
      />,
    );

    expect(screen.getByTestId('back-button')).toHaveTextContent('< Tenant Details');
    fireEvent.click(screen.getByTestId('back-button'));
    expect(mockHandleBack).toHaveBeenCalled();
  });

  it('disables Next button when shouldDisableNext is true', () => {
    (useFormikContext as Mock).mockReturnValueOnce({
      isValid: false,
      dirty: false,
      handleSubmit: mockHandleSubmit,
      values: {
        company: '',
        key: '',
        firstName: '',
        lastName: '',
        userName: '',
        email: '',
        city: '',
        state: '',
        isUserNameValid: false,
        userComboError: null,
        overAllPlan: null,
      },
      touched: {
        company: false,
        key: false,
        firstName: false,
        lastName: false,
        userName: false,
        email: false,
        city: false,
        state: false,
      },
    });

    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={0}
      />,
    );

    expect(screen.getByTestId(nextButtonTestId)).toBeDisabled();
  });

  it('disables Next button when required fields are not filled', () => {
    (useFormikContext as Mock).mockReturnValueOnce({
      isValid: true,
      dirty: true,
      handleSubmit: mockHandleSubmit,
      values: {
        company: 'Test Company',
        key: '', // Missing required field
        firstName: 'John',
        lastName: 'Doe',
        userName: 'johndoe',
        email: '<EMAIL>',
        city: 'Test City',
        state: 'CA',
        isUserNameValid: true,
        userComboError: null,
        overAllPlan: {id: 'plan-1'},
      },
      touched: {
        company: true,
        key: true,
        firstName: true,
        lastName: true,
        userName: true,
        email: true,
        city: true,
        state: true,
      },
    });

    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={0}
      />,
    );

    expect(screen.getByTestId(nextButtonTestId)).toBeDisabled();
  });

  it('triggers handleCancel when Cancel button is clicked', () => {
    render(
      <RenderButton
        handleBack={mockHandleBack}
        handleNext={mockHandleNext}
        handleCancel={mockHandleCancel}
        activeStep={0}
      />,
    );

    fireEvent.click(screen.getByTestId('cancel-button'));
    expect(mockHandleCancel).toHaveBeenCalled();
  });
});
