// @vitest-environment jsdom
import React from 'react';
import {describe, expect, it, vi} from 'vitest';
import * as utils from './tenants.utils';

vi.mock('./TenantPage', () => ({
  ActionButtons: Object.assign((props: any) => <div data-testid="action-buttons" {...props} />, {
    displayName: 'ActionButtons',
  }),
}));

function isReactElement(node: any): node is React.ReactElement {
  return typeof node === 'object' && node !== null && 'props' in node;
}

function isFunction(val: unknown): val is Function {
  return typeof val === 'function';
}

describe('tenants.utils', () => {
  it('getStatusLabel returns correct label for all statuses', () => {
    expect(utils.getStatusLabel(utils.TenantStatus.ACTIVE)).toBe('Active');
    expect(utils.getStatusLabel(utils.TenantStatus.PENDINGPROVISION)).toBe('Pending Provision');
    expect(utils.getStatusLabel(utils.TenantStatus.INACTIVE)).toBe('Inactive');
    expect(utils.getStatusLabel(utils.TenantStatus.PROVISIONFAILED)).toBe('Provision Failed');
    expect(utils.getStatusLabel(utils.TenantStatus.PROVISIONING)).toBe('Provisioning');
    expect(utils.getStatusLabel(utils.TenantStatus.PENDINGONBOARDING)).toBe('Pending Onboarding');
    expect(utils.getStatusLabel(999 as unknown as utils.TenantStatus)).toBe('');
  });

  it('getBackendColumnName returns mapped or original column name', () => {
    expect(utils.getBackendColumnName('tenantName')).toBe('name');
    expect(utils.getBackendColumnName('status')).toBe('status');
    expect(utils.getBackendColumnName('createdDate')).toBe('createdOn');
    expect(utils.getBackendColumnName('planName')).toBe('name');
    expect(utils.getBackendColumnName('unknown')).toBe('unknown');
  });

  it('tenantTableColumns: status cell renders StatusChip with correct props', () => {
    const columns = utils.tenantTableColumns(() => {});
    const col = columns.find(c => c.id === 'status')!;
    const context = {
      getValue: () => utils.TenantStatus.ACTIVE,
    } as any;
    const result = col.cell!(context);
    expect(isReactElement(result)).toBe(true);
    if (isReactElement(result)) {
      const props = result.props as any;
      expect(props.label).toBe('Active');
      expect(props.status).toBe(utils.tenantStatusMapToStatusChip[utils.TenantStatus.ACTIVE]);
    }
  });

  it('tenantTableColumns: createdDate cell renders formatted date', () => {
    const columns = utils.tenantTableColumns(() => {});
    const col = columns.find(c => c.id === 'createdDate')!;
    const context = {
      row: {original: {createdOn: '2024-01-02T00:00:00Z'}},
    } as any;
    const result = col.cell!(context);
    expect(result).toMatch(/2 Jan 2024/);
  });

  it('tenantTableColumns: planName cell renders planName or dash', () => {
    vi.mock('./TenantDetails/EllipsisText/EllipsisText', () => ({
      __esModule: true,
      default: (props: any) => <div data-testid="ellipsis-text" {...props} />,
    }));

    const columns = utils.tenantTableColumns(() => {});
    const col = columns.find(c => c.id === 'planName')!;
    const contextWithPlan = {row: {original: {planName: 'Gold'}}} as any;
    const contextWithoutPlan = {row: {original: {planName: undefined}}} as any;

    const resultWithPlan = col.cell!(contextWithPlan);
    const resultWithoutPlan = col.cell!(contextWithoutPlan);

    function getText(node: any) {
      if (node && typeof node === 'object' && 'props' in node && node.props && 'text' in node.props) {
        return node.props.text;
      }
      return node;
    }

    expect(getText(resultWithPlan)).toBe('Gold');
    expect(getText(resultWithoutPlan)).toBe('-');
  });

  it('tenantTableColumns: actions cell renders ActionButtons', () => {
    const columns = utils.tenantTableColumns(() => {});
    const col = columns.find(c => c.header === 'Actions')!;
    const context = {} as any;
    const result = col.cell!(context);
    expect(isReactElement(result)).toBe(true);
    if (isReactElement(result) && isFunction(result.type)) {
      expect((result.type as any).displayName).toBe('ActionButtons');
    }
  });
});

it('tenantTableColumns: tenantName cell renders name or dash', () => {
  vi.mock('./TenantDetails/EllipsisText/EllipsisText', () => ({
    __esModule: true,
    default: (props: any) => <div data-testid="ellipsis-text" {...props} />,
  }));

  const columns = utils.tenantTableColumns(() => {});
  const col = columns.find(c => c.id === 'tenantName')!;
  const contextWithName = {row: {original: {name: 'Acme Corp'}}} as any;
  const contextWithoutName = {row: {original: {name: ''}}} as any;
  const contextUndefinedName = {row: {original: {name: undefined}}} as any;

  function getText(node: any) {
    if (node && typeof node === 'object' && 'props' in node && node.props && 'text' in node.props) {
      return node.props.text;
    }
    return node;
  }

  expect(getText(col.cell!(contextWithName))).toBe('Acme Corp');
  expect(getText(col.cell!(contextWithoutName))).toBe('-');
  expect(getText(col.cell!(contextUndefinedName))).toBe('-');
});
