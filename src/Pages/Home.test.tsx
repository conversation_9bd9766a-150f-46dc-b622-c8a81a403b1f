/* @vitest-environment jsdom */
import {fireEvent, screen, waitFor} from '@testing-library/react';
import {dashboardTenantStatuses, TenantStatus} from 'redux/app/types/tenant.type';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, vi} from 'vitest';
import Home from './Home';

// Create mock functions that we can control
const mockNavigate = vi.fn();
const mockUseGetTenantStatusMetricsQuery = vi.fn();
const mockUseGetUserQuery = vi.fn();
const mockHasPermission = vi.fn();

// Mock dependencies
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetTenantStatusMetricsQuery: () => mockUseGetTenantStatusMetricsQuery(),
}));
vi.mock('redux/auth/authApiSlice', () => ({
  useGetUserQuery: () => mockUseGetUserQuery(),
}));
vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', () => ({
  usePermissions: () => ({
    hasPermission: mockHasPermission,
  }),
}));
vi.mock('react-router', () => ({
  useNavigate: () => mockNavigate,
}));
vi.mock('./Dashboard/DashboardTable', () => ({
  __esModule: true,
  default: () => <div data-testid="DashboardTable" />,
}));
vi.mock('./Dashboard/TenantOverview', () => ({
  __esModule: true,
  default: () => <div data-testid="TenantOverview" />,
}));
vi.mock('./Dashboard/CustomBarChart/CustomBarChart', () => ({
  default: ({onClick, data}: {onClick?: (data: any) => void; data: any[]}) => (
    <div data-testid="CustomBarChart">
      {data.map((item, index) => (
        <button
          key={index}
          data-testid={`chart-item-${index}`}
          onClick={() => onClick && onClick(item)}
          data-tag={JSON.stringify(item.tag)}
        >
          {item.name}: {item.value}
        </button>
      ))}
    </div>
  ),
}));
vi.mock('Components/BackdropLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="backdrop-loader" />,
}));

describe('Home', () => {
  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks();

    // Default mock implementations
    mockUseGetTenantStatusMetricsQuery.mockReturnValue({
      data: {
        status: {
          '0': {count: 5, status: 'Active'},
          '1': {count: 2, status: 'Pending Provision'},
        },
        tenants: [
          {id: 1, name: 'Tenant 1'},
          {id: 2, name: 'Tenant 2'},
        ],
        billingStatusMetrics: {
          PAID: 10,
          UNPAID: 5,
        },
      },
      isLoading: false,
      error: undefined,
    });

    mockUseGetUserQuery.mockReturnValue({
      data: {firstName: 'john'},
      isLoading: false,
    });

    mockHasPermission.mockReturnValue(true);
  });

  it('should render the HomePage container', () => {
    renderWithTheme(<Home />);
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });

  it('should render the greeting with user name', async () => {
    renderWithTheme(<Home />);
    await waitFor(() => {
      expect(screen.getByText(/Hi John!👋/i)).toBeInTheDocument();
    });
  });

  it('should render TenantOverview and DashboardTable', () => {
    renderWithTheme(<Home />);
    expect(screen.getByTestId('TenantOverview')).toBeInTheDocument();
    expect(screen.getByTestId('DashboardTable')).toBeInTheDocument();
  });

  it('should render the BarChart', () => {
    renderWithTheme(<Home />);
    expect(screen.getAllByTestId('CustomBarChart').length).greaterThan(0);
  });

  it('should filter statusMetrics to include at least all dashboardTenantStatuses', () => {
    // Create a statusMetrics object with all possible TenantStatus keys (as string)
    const allStatuses = Object.values(TenantStatus)
      .filter(v => typeof v === 'number')
      .map(v => v as number);

    const statusMetrics = Object.fromEntries(
      allStatuses.map(key => [key.toString(), {count: 1, status: TenantStatus[key as TenantStatus]}]),
    );

    // Simulate the filter logic from Home.tsx
    const filteredKeys = Object.keys(statusMetrics).filter(key => dashboardTenantStatuses.has(Number(key)));

    // Assert that all dashboardTenantStatuses are present in the filtered result
    dashboardTenantStatuses.forEach((status: TenantStatus) => {
      expect(filteredKeys).toContain(status.toString());
    });
  });

  // Test loading state (line 167)
  it('should show BackdropLoader when loading', () => {
    mockUseGetTenantStatusMetricsQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: undefined,
    });

    renderWithTheme(<Home />);
    expect(screen.getByTestId('backdrop-loader')).toBeInTheDocument();
  });

  // Test error state (lines 171-172)
  it('should show error message when there is an error', () => {
    mockUseGetTenantStatusMetricsQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: {message: 'API Error'},
    });

    renderWithTheme(<Home />);
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  // Test user loading state
  it('should show loading dots when user is loading', () => {
    mockUseGetUserQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
    });

    renderWithTheme(<Home />);
    // Check that the loading state is rendered (the component should render with loading user)
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });

  // Test statusMetrics with null data (line 46)
  it('should handle null statusMetrics data', () => {
    mockUseGetTenantStatusMetricsQuery.mockReturnValue({
      data: {
        status: null,
        tenants: [],
        billingStatusMetrics: null,
      },
      isLoading: false,
      error: undefined,
    });

    renderWithTheme(<Home />);
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });

  // Test "other" statuses grouping (lines 58-59, 63-78)
  it('should group non-dashboard statuses into "Other" category', () => {
    mockUseGetTenantStatusMetricsQuery.mockReturnValue({
      data: {
        status: {
          '0': {count: 5, status: 'Active'}, // Dashboard status
          '6': {count: 3, status: 'Suspended'}, // Non-dashboard status
          '7': {count: 2, status: 'Overdue'}, // Non-dashboard status
        },
        tenants: [],
        billingStatusMetrics: {},
      },
      isLoading: false,
      error: undefined,
    });

    renderWithTheme(<Home />);
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });

  // Test billing status metrics processing (lines 86-97)
  it('should process billing status metrics correctly', () => {
    mockUseGetTenantStatusMetricsQuery.mockReturnValue({
      data: {
        status: {},
        tenants: [],
        billingStatusMetrics: {
          PAID: 15,
          UNPAID: 8,
          DRAFT: 3,
        },
      },
      isLoading: false,
      error: undefined,
    });

    renderWithTheme(<Home />);
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });

  // Test that charts render with onClick handlers when permissions are available
  it('should render charts with onClick handlers when user has permissions', () => {
    mockHasPermission.mockReturnValue(true);

    renderWithTheme(<Home />);

    // Should render both charts
    expect(screen.getAllByTestId('CustomBarChart')).toHaveLength(2);
  });

  // Test data processing with mixed dashboard and non-dashboard statuses
  it('should process mixed dashboard and non-dashboard statuses correctly', () => {
    mockUseGetTenantStatusMetricsQuery.mockReturnValue({
      data: {
        status: {
          '0': {count: 5, status: 'Active'}, // Dashboard status
          '6': {count: 3, status: 'Suspended'}, // Non-dashboard status
          '7': {count: 2, status: 'Overdue'}, // Non-dashboard status
        },
        tenants: [],
        billingStatusMetrics: {
          PAID: 10,
          UNPAID: 5,
        },
      },
      isLoading: false,
      error: undefined,
    });

    renderWithTheme(<Home />);
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });

  // Test permission-based click handling (line 116)
  it('should not navigate when user lacks tenant permission and status is empty', () => {
    mockHasPermission.mockImplementation(permission => {
      return permission !== '10207'; // ViewTenant permission
    });

    renderWithTheme(<Home />);

    const chartItems = screen.getAllByTestId(/chart-item-/);
    if (chartItems.length > 0) {
      fireEvent.click(chartItems[0]);
      // Should not navigate if no permission and no status
    }
  });

  // Test permission-based click handling for billing (line 128)
  it('should not navigate when user lacks billing permission and status is empty', () => {
    mockHasPermission.mockImplementation(permission => {
      return permission !== 'ViewTenantBillings'; // ViewTenantBillings permission
    });

    renderWithTheme(<Home />);

    const chartItems = screen.getAllByTestId(/chart-item-/);
    if (chartItems.length > 1) {
      fireEvent.click(chartItems[1]);
      // Should not navigate if no permission and no status
    }
  });

  // Test chart rendering without onClick when no permissions
  it('should render charts without onClick handlers when user lacks permissions', () => {
    mockHasPermission.mockReturnValue(false);

    renderWithTheme(<Home />);
    expect(screen.getAllByTestId('CustomBarChart')).toHaveLength(2);
  });

  // Test empty billing status metrics
  it('should handle empty billing status metrics', () => {
    mockUseGetTenantStatusMetricsQuery.mockReturnValue({
      data: {
        status: {},
        tenants: [],
        billingStatusMetrics: {},
      },
      isLoading: false,
      error: undefined,
    });

    renderWithTheme(<Home />);
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });

  // Test undefined billing status metrics
  it('should handle undefined billing status metrics', () => {
    mockUseGetTenantStatusMetricsQuery.mockReturnValue({
      data: {
        status: {},
        tenants: [],
        billingStatusMetrics: undefined,
      },
      isLoading: false,
      error: undefined,
    });

    renderWithTheme(<Home />);
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });
});
