import {Box, Grid, Stack} from '@mui/material';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import Form from 'Components/Forms/Form';
import {getFullName} from 'Helpers/utils';
import {useSnackbar} from 'notistack';
import React from 'react';
import {useNavigate} from 'react-router-dom';
import {useCreateLeadMutation} from 'redux/app/leadManagementApiSlice';
import {RouteNames} from 'Routes/routeNames';
import {gridContainerStyle} from 'styles/pages/AddPlan.styles';
import PlanFormButtons from './FormButtons';
import FormFields from './FormFields';
import {
  addPendingTenantValidationSchema,
  FormAddPendingTenant,
  initialAddPendingTenantValues,
} from './pendingTenants.utils';

const breadcrumbItems = [
  {label: 'Pending Tenants', url: RouteNames.PENDING_TENANTS},
  {label: 'Add Pending Tenant', url: '/add-pending-tenant'},
];

function getBreadCrumbItems() {
  return breadcrumbItems;
}

/**
 * Component for adding a pending tenant (lead) to the system.
 *
 * Renders a form for entering tenant details, validates input, and submits the data via an API mutation.
 * Displays success or error notifications using a snackbar, and navigates back to the pending tenants list on completion or cancellation.
 *
 * @component
 *
 * @returns {JSX.Element} The AddPendingTenant page component.
 *
 * @remarks
 * - Uses `useCreateLeadMutation` for API interaction.
 * - Utilizes `useSnackbar` for notifications.
 * - Navigates using `useNavigate`.
 * - Handles form validation and submission.
 */
const AddPendingTenant: React.FC = () => {
  const navigate = useNavigate();

  const {enqueueSnackbar} = useSnackbar();

  const [createLead, {isLoading: isCreating}] = useCreateLeadMutation();

  const handleCancel = () => {
    navigate(RouteNames.PENDING_TENANTS);
  };

  const handleSubmit = async (values: FormAddPendingTenant) => {
    const formValues = {
      ...values,
      designation: values.designation?.trim(),
      firstName: values.firstName.trim(),
      lastName: values.lastName.trim(),
      companyName: values.companyName.trim(),
      countryCode: values.countryCode.code.trim(),
      city: values.city ?? '',
      state: values.stateId ?? '',
    };
    delete formValues.stateId;

    try {
      const res = await createLead(formValues).unwrap();
      if (res.error) {
        enqueueSnackbar(res.error, {variant: 'error'});
        return;
      }
      enqueueSnackbar('Pending tenant added successfully!', {
        variant: 'success',
        subMessage: getFullName(values) + ' - ' + values.companyName,
      });
      navigate(RouteNames.PENDING_TENANTS);
    } catch {
      /* ignore error */
    }
  };

  return (
    <Box data-testid="AddPlanPage">
      <Grid container>
        <Grid size={12}>
          <Stack spacing={2}>
            <Breadcrumb items={getBreadCrumbItems()} separator="|" showHeader />
          </Stack>
        </Grid>

        <Grid sx={{...gridContainerStyle, width: '100%'}}>
          <Form<FormAddPendingTenant>
            initialValues={{...initialAddPendingTenantValues}}
            validationSchema={addPendingTenantValidationSchema}
            onSubmit={handleSubmit}
            validateOnChange={true}
            validateOnBlur={true}
          >
            <Grid container size={12} spacing={1} rowSpacing={2} sx={{padding: 0, paddingBottom: 12}}>
              <FormFields />
            </Grid>
            <Grid container size={12} spacing={2} rowSpacing={2} sx={{margin: 0, padding: 0}}>
              <PlanFormButtons isLoading={isCreating} onCancel={handleCancel} />
            </Grid>
          </Form>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AddPendingTenant;
