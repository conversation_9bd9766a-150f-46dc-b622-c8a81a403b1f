import {Box, CircularProgress, Stack, Tooltip, Typography} from '@mui/material';
import EyeIcon from 'Assets/EyeIcon';
import {DebouncedInput, Table} from 'Components/Table';
import {useSnackbar} from 'notistack';
import React, {useState} from 'react';
import {useNavigate} from 'react-router-dom';

import AddIcon from '@mui/icons-material/Add';
import {CellContext} from '@tanstack/react-table';
import SearchIcon from 'Assets/search-icon.svg';
import FilterIcon from 'Assets/tenant-filter-icon.svg';
import BlueButton from 'Components/BlueButton/BlueButton';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import {useGetLeadsCountQuery, useGetLeadsQuery, useUpdateLeadByIdMutation} from 'redux/app/leadManagementApiSlice';

import ConvertCheckIcon from 'Assets/ConvertCheckIcon';
import InvalidIcon from 'Assets/InvalidIcon';
import BorderButton from 'Components/BorderButton/BorderButton';
import Filter, {IFilter} from 'Components/Filter/Filter';
import PermissionWrapper from 'Components/PermissionWrapper';
import {useTableState} from 'Components/Table/hook/TableStateHook';
import PermissionKey from 'Constants/enums/permissions';
import {getFullName} from 'Helpers/utils';
import {getStateOptions, renderFilterButton} from 'Pages/utils';
import {LeadApiDTO, LeadApiForCountDTO} from 'redux/app/types';
import {RouteNames} from 'Routes/routeNames';
import {
  bodyCellProps,
  coloumnCellProps,
  headerBoxStyle,
  leftHeaderStyle,
  tableContainerProps,
  tableHeadProps,
  toolTipStyles,
} from 'styles/pages/TenantPage.styles';
import {InvalidDialog} from './InvalidDialog';
import {getBackendColumnName, LeadStatus, tenantTableColumns} from './pendingTenants.utils';
import FilterStatusChips from './PendingTenantsFilterChip';

interface IActionButtonsProps {
  row: CellContext<unknown, unknown>;
}

const getColor = (leadStatus: LeadStatus) =>
  leadStatus === LeadStatus.CONVERTED || leadStatus === LeadStatus.INVALID ? 'body.200' : 'body.500';

/**
 * Returns the styles for action buttons based on the lead status.
 *
 * @param leadStatus - The current status of the lead.
 * @param iconName - Optional icon name to apply specific styles.
 * @returns The style object for the action button.
 */
export const actionStyles = (leadStatus: LeadStatus, iconName?: string) => {
  if (iconName === 'view') {
    return {
      color: 'body.500',
      fontSize: '1.25rem',
      fill: 'body.500',
      mr: 1.5,
      cursor: 'pointer',
    };
  }
  return {
    color: getColor(leadStatus),
    fontSize: '1.25rem',
    fill: getColor(leadStatus),
    mr: 1.5,
    cursor: leadStatus === LeadStatus.CONVERTED || leadStatus === LeadStatus.INVALID ? 'not-allowed' : 'pointer',
  };
};

/**
 * Renders action buttons for each pending tenant lead row, providing options to view details,
 * convert the lead to a tenant, or mark the lead as invalid.
 *
 * - "View details" navigates to the pending tenant's detail page.
 * - "Convert To Tenant" opens a dialog to convert the lead, fetching state and city IDs before navigating to the tenant addition page.
 * - "Mark as invalid" opens a dialog to mark the lead as invalid, updating its status and showing a notification.
 *
 * Handles dialog state, lead status updates, and navigation.
 *
 * @param row - The row data for the pending tenant lead.
 * @param refetchLeads - Callback to refetch leads after status update.
 * @returns The rendered action buttons and dialog component.
 */
export const ActionButtons: React.FC<IActionButtonsProps & {refetchLeads: () => void}> = ({row, refetchLeads}) => {
  const navigate = useNavigate();
  const [openDialog, setOpenDialog] = useState(false);
  const [actionType, setActionType] = useState<string | null>(null);

  const {enqueueSnackbar} = useSnackbar();
  const [updateStatus, {isLoading: isUpdatingStatus}] = useUpdateLeadByIdMutation();

  const handleRedirectToDetails = (tenantId: string) => {
    navigate(`/pending-tenants/${tenantId}`);
  };

  const handleDialog = (str: string) => {
    setOpenDialog(true);
    setActionType(str);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  /**
   * Handles the invalid dialog action for a pending tenant lead.
   *
   * Depending on the `actionType`, this function either:
   * - Converts the lead by gathering required information, fetching state and city IDs,
   *   and navigating to the tenant addition page with the lead info.
   * - Marks the lead as invalid by updating its status and showing a success notification.
   *
   * @param leadId - The unique identifier of the lead.
   * @param companyName - The name of the company associated with the lead.
   * @returns A promise that resolves when the action is completed.
   */
  const handleInvalidDialog = async (leadId: string, companyName: string) => {
    if (actionType === 'convert') {
      const stateId = await getStateOptions((row.row.original as {address: {state: string}}).address.state);
      const leadInfo = {
        leadId: leadId,
        firstName: (row.row.original as {firstName: string}).firstName,
        lastName: (row.row.original as {lastName: string}).lastName,
        companyName: (row.row.original as {companyName: string}).companyName,
        email: (row.row.original as {email: string}).email,
        designation: (row.row.original as {designation: string}).designation,
        phoneNumber: (row.row.original as {phoneNumber: string}).phoneNumber,
        countryCode: (row.row.original as {countryCode: string}).countryCode,
        city: (row.row.original as {address: {city: string}}).address.city,
        state: (row.row.original as {address: {state: string}}).address.state,
        stateId: stateId,
      };
      navigate(RouteNames.ADD_TENANT, {state: {leadInfo: leadInfo}});
      setOpenDialog(false);
    } else {
      updateStatus({leadId: leadId, status: LeadStatus.INVALID})
        .then(result => {
          if (result.error) {
            setOpenDialog(false);
            return;
          }
          enqueueSnackbar(`Pending tenant marked as invalid successfully!`, {
            variant: 'success',
            subMessage: getFullName(row.row.original) + ' - ' + companyName,
          });
          setOpenDialog(false);
          refetchLeads();
        })
        .catch(() => {});
    }
  };

  const leadStatus = (row.row.original as {status: LeadStatus}).status;

  return (
    <Stack display="flex" flexDirection={'row'}>
      <Tooltip title="View details" placement="top" arrow slotProps={toolTipStyles}>
        <EyeIcon
          sx={actionStyles(leadStatus, 'view')}
          onClick={() => handleRedirectToDetails((row.row.original as {id: string}).id)}
        />
      </Tooltip>
      <PermissionWrapper permission={PermissionKey.UpdateLead}>
        <Tooltip title="Convert To Tenant" placement="top" arrow slotProps={toolTipStyles}>
          <ConvertCheckIcon
            sx={actionStyles(leadStatus)}
            data-testid="convert-button"
            onClick={() => handleDialog('convert')}
          />
        </Tooltip>
      </PermissionWrapper>
      <PermissionWrapper permission={PermissionKey.UpdateLead}>
        <Tooltip title="Mark as invalid" placement="top" arrow slotProps={toolTipStyles}>
          <InvalidIcon
            sx={actionStyles(leadStatus)}
            data-testid="invalid-button"
            onClick={() => handleDialog('invalid')}
          />
        </Tooltip>
      </PermissionWrapper>
      {openDialog && (
        <InvalidDialog
          onConfirm={() =>
            handleInvalidDialog(
              (row.row.original as {id: string}).id,
              (row.row.original as {companyName: string}).companyName,
            )
          }
          actionType={actionType as 'invalid' | 'convert'}
          open={openDialog}
          onClose={handleDialogClose}
          isLoading={isUpdatingStatus}
          title={
            (row.row.original as {companyName: string}).companyName +
            ' - ' +
            (row.row.original as {firstName: string}).firstName +
            ' ' +
            (row.row.original as {lastName: string}).lastName
          }
        />
      )}
    </Stack>
  );
};
const buttonHeight = '2.375rem';

/**
 * PendingTenant component displays a paginated, sortable, and filterable table of pending tenants.
 *
 * Features:
 * - Fetches tenant data and count from API with search, filter, and sort parameters.
 * - Handles pagination, sorting, and search input with debounce.
 * - Displays error notifications on API failures.
 * - Provides UI controls for adding a new pending tenant and applying filters.
 * - Shows a loading indicator while data is being fetched.
 *
 * State:
 * - `searchTerm`: Current search input value.
 * - `sortBy`: Current sort order for API requests.
 * - `selectedPendingTenantFilter`: Current filter selection.
 * - `openFilter`: Controls visibility of the filter dialog.
 *
 * Effects:
 * - Resets pagination offset when search/filter changes.
 * - Shows error notifications on API errors.
 * - Initializes sort order on mount.
 *
 * API:
 * - Uses `useGetLeadsQuery` and `useGetLeadsCountQuery` for data fetching.
 *
 * UI:
 * - Renders a table of tenants with sorting and pagination.
 * - Renders top-right controls for search, filter, and add actions.
 * - Integrates filter dialog and status chips.
 *
 * @component
 */
const PendingTenant: React.FC = () => {
  const {enqueueSnackbar} = useSnackbar();
  const {limit, setLimit, offset, setOffset, handlePageChange, handleRowsPerPageChange} = useTableState();
  const [searchTerm, setSearchTerm] = useState('');

  const [sortBy, setSortBy] = useState<string>('createdOn DESC');

  // Initial sort state for the table - use frontend column ID
  const initialSortState = [{id: 'createdDate', desc: true}];

  const [selectedPendingTenantFilter, setSelectedPendingTenantFilter] = React.useState<IFilter | undefined>(undefined);
  const filterButtonRef = React.useRef<HTMLButtonElement>(null);
  const [openFilter, setOpenFilter] = React.useState(false);

  // Remove the useEffect that resets offset on search/filter change
  // Reset offset only when search/filter changes, but do not trigger on page change
  React.useEffect(() => {
    setOffset(0);
  }, [searchTerm]);

  const handleSortChange = (columnId: string, sort: boolean) => {
    // Map frontend column name to backend column name
    const backendColumnName = getBackendColumnName(columnId);
    const sortParam = `${backendColumnName} ${sort ? 'DESC' : 'ASC'}`;
    setOffset(0);
    setSortBy(sortParam);
  };

  // Build the filter object for the API call
  const filterParams: LeadApiDTO = {
    limit,
    offset,
    order: sortBy,
    searchValue: searchTerm,
    status: selectedPendingTenantFilter?.status
      ? Array.from(selectedPendingTenantFilter.status).map(Number)
      : undefined,
    dateRange: selectedPendingTenantFilter?.dateRange,
  };

  // Build count filter (without limit/offset)
  const countFilterParams: LeadApiForCountDTO = {
    order: sortBy,
    searchValue: searchTerm,
    status: selectedPendingTenantFilter?.status
      ? Array.from(selectedPendingTenantFilter.status).map(Number)
      : undefined,
    dateRange: selectedPendingTenantFilter?.dateRange,
  };

  const {
    data: leads,
    error: leadsError,
    isFetching: isLoading,
    refetch: refetchLeads,
  } = useGetLeadsQuery(filterParams, {
    refetchOnMountOrArgChange: true,
  });
  const {
    data: leadsCount,
    error: countError,
    isFetching: countLoading,
  } = useGetLeadsCountQuery(countFilterParams, {
    refetchOnMountOrArgChange: true,
  });
  // Show error notifications
  React.useEffect(() => {
    if (leadsError) {
      enqueueSnackbar('Failed to fetch pending tenants data', {variant: 'error'});
    }
    if (countError) {
      enqueueSnackbar('Failed to fetch pending tenants count', {variant: 'error'});
    }
  }, [leadsError, countError, enqueueSnackbar]);
  React.useEffect(() => {
    handleSortChange('createdDate', true);
  }, []);

  const navigate = useNavigate();

  const handleRedirect = () => {
    navigate(RouteNames.ADD_PENDING_TENANT);
  };

  const buildTopRightSection = () => {
    return (
      <Box sx={{display: 'flex', gap: 1, flexDirection: 'row', alignItems: 'center', ml: 'auto'}}>
        <DebouncedInput
          placeholder="Search pending tenants"
          data-testid="search-pending-tenant"
          sx={{
            fontSize: '0.675rem',
            pl: 0,
          }}
          debounceTime={500}
          leftAdornment={<SVGImageFromPath path={SearchIcon} sx={{width: '1rem', height: '1rem', mr: 1}} />}
          inputSx={{
            fontSize: '1rem',
            fontWeight: 400,
            color: 'black.main',
          }}
          value={searchTerm}
          onChange={value => {
            setSearchTerm('' + value);
          }}
        />
        {renderFilterButton({
          filterButtonRef,
          buttonHeight,
          filterSize:
            (selectedPendingTenantFilter?.status?.size ?? 0) + (selectedPendingTenantFilter?.dateRange ? 1 : 0),
          hasFilters: !!(
            (selectedPendingTenantFilter?.status?.size ?? 0) + (selectedPendingTenantFilter?.dateRange ? 1 : 0)
          ),
          setOpenFilter,
          FilterIcon,
          BorderButton,
          SVGImageFromPath,
        })}

        <PermissionWrapper permission={PermissionKey.CreateLead}>
          <BlueButton
            sx={{height: buttonHeight, fontSize: '0.8125rem', fontWeight: 700, pl: 2, pr: 2.5}}
            onClick={handleRedirect}
          >
            <Box sx={{flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1}}>
              <AddIcon sx={{height: '1rem', width: '1rem'}} />
              Add Pending Tenant
            </Box>
          </BlueButton>
        </PermissionWrapper>
      </Box>
    );
  };

  return (
    <Box>
      <Box sx={headerBoxStyle}>
        <Typography variant="h6" sx={leftHeaderStyle}>
          Pending tenants
        </Typography>
        {buildTopRightSection()}
      </Box>
      <Box sx={{position: 'relative', minHeight: '200px'}}>
        <Table
          data={leads || []}
          columns={tenantTableColumns(refetchLeads)}
          enableSorting={true}
          initialSortingState={initialSortState}
          tablePropsObject={{
            tableHeadProps: {sx: tableHeadProps},
            columnCellProps: {sx: coloumnCellProps},
            tableContainerProps: {sx: tableContainerProps},
            bodyCellProps: {sx: bodyCellProps},
          }}
          limit={limit}
          setLimit={setLimit}
          offset={offset}
          setOffset={setOffset}
          count={leadsCount?.count || 0}
          manualPagination={true}
          onSortChange={handleSortChange}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          data-testid="pending-tenant-table"
        />
        {(isLoading || countLoading) && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'rgba(255, 255, 255, 0.7)',
              zIndex: 1,
            }}
          >
            <CircularProgress />
          </Box>
        )}
      </Box>
      <Filter
        open={openFilter}
        value={selectedPendingTenantFilter}
        onClose={() => setOpenFilter(false)}
        anchorEl={filterButtonRef.current}
        onFilterChange={filter => {
          setOffset(0);
          setSelectedPendingTenantFilter(filter);
        }}
        FilterStatusChips={FilterStatusChips}
      />
    </Box>
  );
};

export default PendingTenant;
