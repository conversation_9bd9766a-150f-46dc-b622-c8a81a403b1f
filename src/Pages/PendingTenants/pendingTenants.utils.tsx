import {CellContext} from '@tanstack/react-table';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import StatusChip from 'Components/StatusChip/StatusChip';
import {Integers} from 'Helpers/integers';
import {dateFormatter} from 'Helpers/utils';
import {LeadType} from 'redux/app/types';
import * as yup from 'yup';
import {ActionButtons} from './PendingTenantPage';

export const DEFAULT_LIMIT = 20;
export const DEFAULT_OFFSET = 0;

const whiteMain = 'white.main';

export enum LeadStatus {
  PENDING,
  CONVERTED,
  INVALID,
}

export const getStatusColor = (status: LeadStatus): string => {
  const statusColorMap: Record<number, string> = {
    [LeadStatus.PENDING]: 'alert.warning.bg',
    [LeadStatus.CONVERTED]: 'alert.success.bg',
    [LeadStatus.INVALID]: 'alert.error.bg',
  };
  return statusColorMap[status] ?? whiteMain;
};

export const getFontColor = (status: LeadStatus): string => {
  const statusColorMap: Record<LeadStatus, string> = {
    [LeadStatus.PENDING]: 'alert.warning.onBg',
    [LeadStatus.CONVERTED]: 'alert.success.onBg',
    [LeadStatus.INVALID]: 'alert.error.onBg',
  };
  return statusColorMap[status] || whiteMain;
};

export const getIndicatorColor = (status: LeadStatus): string => {
  const statusColorMap: Record<LeadStatus, string> = {
    [LeadStatus.PENDING]: 'alert.warning.main',
    [LeadStatus.CONVERTED]: 'alert.success.main',
    [LeadStatus.INVALID]: 'alert.error.main',
  };
  return statusColorMap[status] || whiteMain;
};

export const getStatusLabel = (status: LeadStatus | number): string => {
  const statusLabelMap: Record<LeadStatus | number, string> = {
    [LeadStatus.PENDING]: 'Pending',
    [LeadStatus.CONVERTED]: 'Converted',
    [LeadStatus.INVALID]: 'Invalid',
  };
  return statusLabelMap[status] || '';
};

interface TenantTableRow extends LeadType {}

interface TenantTableColumn {
  header: string;
  accessorKey?: keyof TenantTableRow;
  id?: string;
  cell?: (context: CellContext<TenantTableRow, unknown>) => React.ReactNode;
}

const columnNameMap: Record<string, string> = {
  companyName: 'companyName',
  status: 'status',
  leadFullName: 'leadFullName',
  leadPhoneNumber: 'leadPhoneNumber',
  leadEmail: 'leadEmail',
  createdDate: 'createdOn',
};

export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

// Format US phone number as +****************
export function formatUSPhoneNumber(countryCode: string, phoneNumber: string | undefined): string {
  if (!phoneNumber || phoneNumber.length !== 10) return '-';
  const area = phoneNumber.slice(0, Integers.Three);
  const central = phoneNumber.slice(Integers.Three, Integers.Six);
  const line = phoneNumber.slice(Integers.Six, Integers.Ten);
  return `${countryCode} (${area}) ${central}-${line}`;
}

export const tenantTableColumns = (refetchLeads: () => void): TenantTableColumn[] => [
  {
    header: 'Pending tenant name',
    accessorKey: 'companyName',
    id: 'companyName',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => (
      <EllipsisText
        text={row.original.companyName && row.original.companyName.length > 0 ? row.original.companyName : '-'}
      />
    ),
  },
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: (context: CellContext<TenantTableRow, unknown>) => {
      const status = context.getValue() as LeadStatus;
      const backgroundColor = getStatusColor(status);
      const color = getFontColor(status);
      const indicatorColor = getIndicatorColor(status);
      const label = getStatusLabel(status);
      return (
        <StatusChip label={label} backgroundColor={backgroundColor} indicatorColor={indicatorColor} color={color} />
      );
    },
  },
  {
    header: 'Name',
    accessorKey: 'firstName',
    id: 'leadFullName',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => {
      const {firstName, lastName} = row.original;
      const name = `${firstName} ${lastName}`;
      return <EllipsisText text={name && name.length > 0 ? name : '-'} />;
    },
  },
  {
    header: 'Phone number',
    accessorKey: 'phoneNumber',
    id: 'leadPhoneNumber',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => {
      const phoneNumber = row.original.phoneNumber;
      const formatedPhoneNumber = phoneNumber ? formatUSPhoneNumber(row.original.countryCode ?? '', phoneNumber) : '-';
      return <EllipsisText text={formatedPhoneNumber} />;
    },
  },
  {
    header: 'Email address',
    accessorKey: 'email',
    id: 'leadEmail',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => {
      const email = row.original.email;
      return <EllipsisText text={email && email.length > 0 ? email : '-'} />;
    },
  },
  {
    header: 'Created date',
    accessorKey: 'createdOn',
    id: 'createdDate',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => dateFormatter(row.original.createdOn),
  },
  {
    header: 'Actions',
    cell: (cellContext: CellContext<TenantTableRow, unknown>) => (
      <ActionButtons row={cellContext as CellContext<unknown, unknown>} refetchLeads={refetchLeads} />
    ),
  },
];

export interface CountryCode {
  code: string;
  label: string;
}

// Extract valid state values for validation
// Regex constants
const COMPANY_NAME_REGEX = /^(?![-\s])[a-zA-Z0-9\s&.,'’-]+(?<![-\s])$/;
const NAME_REGEX = /^[a-zA-Z\s]+$/;
const EMAIL_REGEX = /^[a-zA-Z0-9._%+-]+@([a-zA-Z0-9-]+\.){1,3}[a-zA-Z]{2,7}$/;
const MOBILE_NUMBER_REGEX = /^\d{10}$/;
// Constants
const COMPANY_NAME_MIN = 1;
const COMPANY_NAME_MAX = 50;

const NAME_MIN = 1;
const NAME_MAX = 50;

const DESIGNATION_MIN = 3;
const DESIGNATION_MAX = 50;

const EMAIL_MAX = 254;

const MOBILE_NUMBER_LENGTH = 10;
const noWhiteSpace = 'not-only-whitespace';

export const addPendingTenantValidationSchema = yup.object({
  companyName: yup
    .string()
    .required('Company name is required')
    .min(COMPANY_NAME_MIN, `Company name should have at least ${COMPANY_NAME_MIN} characters`)
    .max(COMPANY_NAME_MAX, `Company name should have at most ${COMPANY_NAME_MAX} characters`)
    .matches(
      COMPANY_NAME_REGEX,
      'Company name should only contain letters, numbers, spaces, and valid punctuation in between.',
    )
    .test(noWhiteSpace, 'Company name cannot be only whitespace', value => !!value?.trim()),

  firstName: yup
    .string()
    .required('First name is required')
    .min(NAME_MIN, `First name should have at least ${NAME_MIN} characters`)
    .max(NAME_MAX, `First name should have at most ${NAME_MAX} characters`)
    .matches(NAME_REGEX, 'First name should only contain letters')
    .test(noWhiteSpace, 'First name cannot be only whitespace', value => !!value?.trim()),

  lastName: yup
    .string()
    .required('Last name is required')
    .min(NAME_MIN, `Last name should have at least ${NAME_MIN} characters`)
    .max(NAME_MAX, `Last name should have at most ${NAME_MAX} characters`)
    .matches(NAME_REGEX, 'Last name should only contain letters')
    .test(noWhiteSpace, 'Last name cannot be only whitespace', value => !!value?.trim()),

  designation: yup
    .string()
    .min(DESIGNATION_MIN, `Job title should have at least ${DESIGNATION_MIN} characters`)
    .max(DESIGNATION_MAX, `Job title should have at most ${DESIGNATION_MAX} characters`)
    .matches(NAME_REGEX, 'Job title should only contain letters')
    .test(noWhiteSpace, 'Job title cannot be only whitespace', value => value === undefined || !!value.trim()),

  email: yup
    .string()
    .required('Email address is required')
    .email('Email address is not in a valid format')
    .max(EMAIL_MAX, `Email should have at most ${EMAIL_MAX} characters`)
    .matches(EMAIL_REGEX, 'Email address is not in a valid format')
    .test(noWhiteSpace, 'Email address cannot be only whitespace', value => !!value?.trim()),

  countryCode: yup
    .object()
    .shape({
      code: yup.string(),
      label: yup.string(),
    })
    .required('Country Code is required'),

  city: yup.string().required('City is required'),
  state: yup.string().required('State is required'),

  phoneNumber: yup
    .string()
    .matches(MOBILE_NUMBER_REGEX, `Mobile Number must be exactly ${MOBILE_NUMBER_LENGTH} digits`)
    .test(noWhiteSpace, 'Mobile Number cannot be only whitespace', value => value === undefined || !!value.trim()),
});

export interface FormAddPendingTenant {
  firstName: string;
  lastName: string;
  companyName: string;
  designation: string | undefined;
  email: string;
  countryCode: CountryCode;
  phoneNumber: string | undefined;
  city: string;
  state: string;
  stateId?: string;
}

export const initialAddPendingTenantValues: FormAddPendingTenant = {
  firstName: '',
  lastName: '',
  companyName: '',
  designation: undefined,
  email: '',
  countryCode: {code: '+1', label: 'USA'},
  phoneNumber: undefined,
  city: '',
  state: '',
  stateId: '',
};
