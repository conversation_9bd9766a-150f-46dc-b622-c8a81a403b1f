// src/Pages/Dashboard/CustomBarChart/CustomBarChart.test.tsx
import {ThemeProvider, createTheme} from '@mui/material/styles';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import CustomBarChart, {CustomTooltip, IBarChartData} from './CustomBarChart';

const mockData: IBarChartData[] = [
  {name: 'A', value: 30, color: '#8884d8', tooltip: 'Alpha'},
  {name: 'B', value: 20, color: '#82ca9d', tooltip: 'Beta'},
];

const mockDataWithMultiWordNames: IBarChartData[] = [
  {name: 'Multi Word Label', value: 30, color: '#8884d8', tooltip: 'Multi word tooltip'},
  {name: 'Another Long Name', value: 20, color: '#82ca9d', tooltip: 'Another tooltip'},
];

const mockDataWithoutTooltip: IBarChartData[] = [{name: 'No Tooltip', value: 15, color: '#ff7300'}];

// Mock ResizeObserver for responsive container tests
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

describe('CustomBarChart', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders no data view when data is empty', () => {
    renderWithTheme(<CustomBarChart data={[]} height={300} />);
    expect(screen.getByText('No record to display')).toBeInTheDocument();
  });

  it('renders no data view when data is null', () => {
    renderWithTheme(<CustomBarChart data={null as any} height={300} />);
    expect(screen.getByText('No record to display')).toBeInTheDocument();
  });

  it('renders chart with data', () => {
    renderWithTheme(<CustomBarChart data={mockData} height={300} />);
    // Check that the chart container is rendered (ResponsiveContainer creates a div)
    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('shows tooltip on hover', async () => {
    renderWithTheme(<CustomBarChart data={mockData} height={300} />);
    // Try to find any SVG element and simulate mouse over
    const svg = document.querySelector('svg');
    if (svg) {
      fireEvent.mouseOver(svg);
      await waitFor(() => {
        expect(screen.getByText('Alpha')).toBeInTheDocument();
      });
    }
  });

  it('calls onClick handler when bar is clicked', async () => {
    const handleClick = vi.fn();
    renderWithTheme(<CustomBarChart data={mockData} height={300} onClick={handleClick} />);
    // Try to find any SVG element and simulate click
    const svg = document.querySelector('svg');
    if (svg) {
      fireEvent.click(svg);
      expect(handleClick).toHaveBeenCalled();
    }
  });

  it('renders chart with multi-word labels', () => {
    renderWithTheme(<CustomBarChart data={mockDataWithMultiWordNames} height={300} />);
    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('renders chart without tooltip fallback to name', () => {
    renderWithTheme(<CustomBarChart data={mockDataWithoutTooltip} height={300} />);
    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('uses default height when not provided', () => {
    renderWithTheme(<CustomBarChart data={mockData} />);
    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('renders chart without onClick handler', () => {
    renderWithTheme(<CustomBarChart data={mockData} height={300} />);
    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });
});

describe('CustomTooltip', () => {
  const theme = createTheme();

  it('renders tooltip when active with payload', () => {
    const mockPayload = [
      {
        payload: {
          name: 'Test Name',
          value: 100,
          color: '#8884d8',
          tooltip: 'Test Tooltip',
        },
      },
    ];

    const props = {
      active: true,
      payload: mockPayload,
    };

    render(
      <ThemeProvider theme={theme}>
        <CustomTooltip {...props} />
      </ThemeProvider>,
    );

    expect(screen.getByText('Test Tooltip')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
  });

  it('renders tooltip with name fallback when tooltip is not provided', () => {
    const mockPayload = [
      {
        payload: {
          name: 'Test Name',
          value: 100,
          color: '#8884d8',
        },
      },
    ];

    const props = {
      active: true,
      payload: mockPayload,
    };

    render(
      <ThemeProvider theme={theme}>
        <CustomTooltip {...props} />
      </ThemeProvider>,
    );

    expect(screen.getByText('Test Name')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
  });

  it('returns empty fragment when not active', () => {
    const props = {
      active: false,
      payload: [],
    };

    const {container} = render(
      <ThemeProvider theme={theme}>
        <CustomTooltip {...props} />
      </ThemeProvider>,
    );

    expect(container.firstChild).toBeNull();
  });

  it('returns empty fragment when payload is empty', () => {
    const props = {
      active: true,
      payload: [],
    };

    const {container} = render(
      <ThemeProvider theme={theme}>
        <CustomTooltip {...props} />
      </ThemeProvider>,
    );

    expect(container.firstChild).toBeNull();
  });
});

describe('Responsive Behavior', () => {
  it('handles resize events for different widths', () => {
    const {container} = renderWithTheme(<CustomBarChart data={mockData} height={300} />);

    // Find the ResponsiveContainer
    const responsiveContainer = container.querySelector('.recharts-responsive-container');
    expect(responsiveContainer).toBeInTheDocument();

    // The ResponsiveContainer should be rendered and handle resize events
    // This ensures the component structure is correct for responsive behavior
    expect(responsiveContainer).toHaveStyle('width: 100%');
  });

  it('handles responsive container onResize callback directly', () => {
    const {container} = renderWithTheme(<CustomBarChart data={mockData} height={300} />);

    const responsiveContainer = container.querySelector('.recharts-responsive-container');
    expect(responsiveContainer).toBeInTheDocument();

    // The ResponsiveContainer should be rendered and handle resize events
    // This ensures the component structure is correct for responsive behavior
    expect(responsiveContainer).toHaveStyle('width: 100%');
  });
});

describe('CustomBarChartTick Component', () => {
  it('renders multi-word labels with proper word wrapping', () => {
    renderWithTheme(<CustomBarChart data={mockDataWithMultiWordNames} height={300} />);

    // The CustomBarChartTick component is used internally by the XAxis
    // We can verify it's working by checking that the chart renders with multi-word data
    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('handles single word labels', () => {
    const singleWordData: IBarChartData[] = [
      {name: 'Single', value: 30, color: '#8884d8'},
      {name: 'Word', value: 20, color: '#82ca9d'},
    ];

    renderWithTheme(<CustomBarChart data={singleWordData} height={300} />);

    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('handles empty string labels', () => {
    const emptyLabelData: IBarChartData[] = [
      {name: '', value: 30, color: '#8884d8'},
      {name: ' ', value: 20, color: '#82ca9d'},
    ];

    renderWithTheme(<CustomBarChart data={emptyLabelData} height={300} />);

    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });
});

describe('Additional Coverage Tests', () => {
  it('renders with custom width and height', () => {
    renderWithTheme(<CustomBarChart data={mockData} width={500} height={400} />);
    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('renders with additional props passed to BarChart', () => {
    renderWithTheme(
      <CustomBarChart data={mockData} height={300} margin={{top: 10, right: 10, left: 10, bottom: 10}} />,
    );
    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('handles data with tag property', () => {
    const dataWithTags: IBarChartData[] = [
      {name: 'A', value: 30, color: '#8884d8', tag: {id: 1, category: 'test'}},
      {name: 'B', value: 20, color: '#82ca9d', tag: {id: 2, category: 'test2'}},
    ];

    renderWithTheme(<CustomBarChart data={dataWithTags} height={300} />);
    const chartContainer = document.querySelector('.recharts-responsive-container');
    expect(chartContainer).toBeInTheDocument();
  });

  it('handles large numbers in tooltip formatting', () => {
    const mockPayload = [
      {
        payload: {
          name: 'Large',
          value: 1000000,
          color: '#8884d8',
          tooltip: 'One Million',
        },
      },
    ];

    const props = {
      active: true,
      payload: mockPayload,
    };

    const theme = createTheme();
    render(
      <ThemeProvider theme={theme}>
        <CustomTooltip {...props} />
      </ThemeProvider>,
    );

    expect(screen.getByText('One Million')).toBeInTheDocument();
    expect(screen.getByText('1,000,000')).toBeInTheDocument();
  });
});
