import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import React, {useEffect, useRef} from 'react';
import {Navigate, useLocation} from 'react-router-dom';
import {useLazyVerifyAuthCodeQuery} from 'redux/auth/authApiSlice';

// Assume you have an RTK Query hook like useVerifyCodeQuery

interface LinkCodeVerificationWrapperProps {
  children: React.ReactNode;
  errorComponent: React.ReactNode;
}

/**
 * A wrapper component that verifies an authentication code from the URL query string.
 *
 * - Extracts the `code` parameter from the URL.
 * - Initiates verification using `verifyAuthCode` only once per mount.
 * - Displays a loading spinner while verification is in progress.
 * - Renders `children` if verification succeeds.
 * - Renders `errorComponent` if verification fails.
 * - Redirects to the home page if no code is present in the URL.
 *
 * @param children - The content to render upon successful verification.
 * @param errorComponent - The component to render if verification fails.
 */
const LinkCodeVerificationWrapper: React.FC<LinkCodeVerificationWrapperProps> = ({children, errorComponent}) => {
  const query = new URLSearchParams(useLocation().search);
  const code = query.get('code') || '';
  const isConsumed = useRef(false);
  const [verifyAuthCode, {isFetching, isSuccess, isError}] = useLazyVerifyAuthCodeQuery();

  useEffect(() => {
    if (isConsumed.current) return;
    isConsumed.current = true;
    if (code) {
      verifyAuthCode({code});
    }
  }, [code, verifyAuthCode]);

  if (isFetching) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }
  if (isSuccess) {
    return <>{children}</>;
  }

  if (isError) {
    return <>{errorComponent}</>;
  }
  if (!code) {
    return <Navigate to="/" replace data-testid="navigate" />;
  }
  return null;
};

export default LinkCodeVerificationWrapper;
