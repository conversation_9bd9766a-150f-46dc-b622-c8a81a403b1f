import {Box, <PERSON>, Typography} from '@mui/material';
import ExpiredLinkIcon from 'Assets/ExpiredLinkedIcon';
import BlueButton from 'Components/BlueButton/BlueButton';
import useConfig from 'Hooks/useConfig';
import React from 'react';
import {<PERSON><PERSON><PERSON>rap<PERSON>, KeyIconContainer, LogoContainer} from 'styles/pages/ForgotPassword.styles';
import {HeaderContainer, LoginCard, LoginContainer, StyledDistekLogo} from '../../styles/pages/Login.styles';

interface PageLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle: React.ReactNode;
  isEmailSent?: boolean;
}

/**
 * PageLayout is a reusable layout component for authentication-related pages.
 *
 * @param {React.ReactNode} children - The content to be rendered inside the layout.
 * @param {string} title - The main title displayed at the top of the page.
 * @param {React.ReactNode} subtitle - Optional subtitle or description shown below the title.
 * @param {boolean} [isEmailSent=false] - Indicates whether an email has been sent (default: false).
 *
 * @returns {JSX.Element} The rendered layout with logo, icon, title, subtitle, and children.
 */
const PageLayout: React.FC<PageLayoutProps> = ({children, title, subtitle, isEmailSent = false}) => (
  <LoginContainer data-testid="ExpiredLinkPage">
    <LoginCard elevation={0}>
      <HeaderContainer>
        <LogoContainer>
          <StyledDistekLogo />
        </LogoContainer>

        <IconWrapper>
          <KeyIconContainer sx={{backgroundColor: 'alert.error.bg'}}>
            <ExpiredLinkIcon sx={{height: 25, fill: 'transparent'}} />
          </KeyIconContainer>
        </IconWrapper>

        <Typography sx={{fontWeight: 700, fontSize: '1.625rem', color: 'body.dark'}}>{title}</Typography>
        {subtitle}
      </HeaderContainer>

      {children}
    </LoginCard>
  </LoginContainer>
);

/**
 * Renders a page indicating that the password reset or access link has expired.
 * Displays a message with a support email address for users to contact support,
 * and provides a button to navigate back to the sign-in page.
 *
 * @returns {JSX.Element} The expired link page component.
 */
const ExpiredLinkPage = () => {
  const {config} = useConfig();

  return (
    <>
      <PageLayout
        title="This link is no longer active."
        subtitle={
          <Box sx={{textAlign: 'center', fontWeight: 400, fontSize: '1.125rem', color: 'body.600', px: 1}}>
            Please reach out to our team at{' '}
            <Link
              href={'mailto:' + (config?.supportEmail ?? '')}
              data-testid="support-email-link"
              sx={{
                color: 'secondary.main',
                fontWeight: 500,
                textDecoration: 'underline',
                textDecorationColor: 'secondary.main',
              }}
            >
              {config?.supportEmail ?? ''}
            </Link>{' '}
            for support.
          </Box>
        }
      >
        <BlueButton
          fullWidth
          component={'a'}
          href="/login"
          sx={{
            fontWeight: 600,
            fontSize: '1rem',
            height: '3.125rem',
            maxHeight: 'unset',
            textTransform: 'capitalize',
          }}
        >
          Go to sign in
        </BlueButton>
      </PageLayout>
      )
    </>
  );
};

export default ExpiredLinkPage;
