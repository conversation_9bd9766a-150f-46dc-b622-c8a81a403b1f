import {render, screen} from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import LinkCodeVerificationWrapper from './LinkCodeVerificationWapper';

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  ...vi.importActual('react-router-dom'),
  useLocation: () => ({
    search: '?code=testcode',
  }),
  Navigate: ({to}: {to: string}) => <div data-testid="navigate">{to}</div>,
}));

// Mock RTK Query hook
const mockVerifyAuthCode = vi.fn();
const mockState = {
  isFetching: false,
  isSuccess: true,
  isError: false,
};
vi.mock('redux/auth/authApiSlice', () => ({
  useLazyVerifyAuthCodeQuery: () => [mockVerifyAuthCode, mockState],
}));

describe('LinkCodeVerificationWrapper', () => {
  it('renders children when verification succeeds', () => {
    render(
      <LinkCodeVerificationWrapper errorComponent={<div>Error</div>}>
        <div data-testid="success-child">Success</div>
      </LinkCodeVerificationWrapper>,
    );
    expect(screen.getByTestId('success-child')).toBeInTheDocument();
  });

  it('renders errorComponent when verification fails', () => {
    // Change mock state to error
    (mockState.isSuccess as any) = false;
    (mockState.isError as any) = true;
    render(
      <LinkCodeVerificationWrapper errorComponent={<div data-testid="error-child">Error</div>}>
        <div>Success</div>
      </LinkCodeVerificationWrapper>,
    );
    expect(screen.getByTestId('error-child')).toBeInTheDocument();
    // Reset for other tests
    (mockState.isSuccess as any) = true;
    (mockState.isError as any) = false;
  });

  it('renders loading spinner when fetching', () => {
    (mockState.isFetching as any) = true;
    (mockState.isSuccess as any) = false;
    render(
      <LinkCodeVerificationWrapper errorComponent={<div>Error</div>}>
        <div>Success</div>
      </LinkCodeVerificationWrapper>,
    );
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    (mockState.isFetching as any) = false;
    (mockState.isSuccess as any) = true;
  });

  it('redirects if code is missing', () => {
    vi.resetModules();
    vi.mock('react-router-dom', () => ({
      ...vi.importActual('react-router-dom'),
      useLocation: () => ({
        search: '',
      }),
      Navigate: ({to}: {to: string}) => <div data-testid="navigate">{to}</div>,
    }));
    render(
      <LinkCodeVerificationWrapper errorComponent={<div>Error</div>}>
        <div>Success</div>
      </LinkCodeVerificationWrapper>,
    );
    expect(screen.getByText('Success')).toBeInTheDocument();
  });
});
