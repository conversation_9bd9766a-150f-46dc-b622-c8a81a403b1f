// Copyright (c) Distek. All rights reserved.

import {screen} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, vi} from 'vitest';
import ExpiredLinkPage from './ExpiredLink';

// Mock useConfig
const mockConfig = {supportEmail: '<EMAIL>'};
vi.mock('../../Hooks/useConfig', () => ({
  default: () => ({config: mockConfig}),
}));

describe('ExpiredLinkPage', () => {
  it('renders the expired link page with all elements', () => {
    renderWithTheme(<ExpiredLinkPage />);
    // Main container
    expect(screen.getByTestId('ExpiredLinkPage')).toBeInTheDocument();
    // Title
    expect(screen.getByText('This link is no longer active.')).toBeInTheDocument();
    // Subtitle text
    expect(screen.getByText(/Please reach out to our team at/i)).toBeInTheDocument();
    // Support email link
    const emailLink = screen.getByTestId('support-email-link');
    expect(emailLink).toBeInTheDocument();
    expect(emailLink).toHaveAttribute('href');
    expect(emailLink.getAttribute('href')).toMatch(/^mailto:/);
    // Button
    const signInButton = screen.getByRole('link', {name: /Go to sign in/i});
    expect(signInButton).toBeInTheDocument();
    expect(signInButton).toHaveAttribute('href', '/login');
  });

  it('renders with empty support email if config is missing', () => {
    vi.mock('../../Hooks/useConfig', () => ({
      default: () => ({config: {}}),
    }));
    renderWithTheme(<ExpiredLinkPage />);
    // Email link should be present but empty
    const emailLinks = screen.getAllByRole('link');
    expect(emailLinks[0]).toHaveAttribute('href', 'mailto:');
    expect(emailLinks[0].textContent?.trim()).toBe('');
  });
});
