import {Integers} from 'Helpers/integers';
import * as yup from 'yup';

// Email validation schema
export const emailValidation = yup
  .string()
  .required('Email is required')
  .email('Please enter a valid email address')
  .matches(/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/, 'Email format is invalid')
  .max(Integers.TwoHundredFiftyFour, 'Email must be less than 255 characters');

// Password validation schema
export const passwordValidation = yup.string().required('Password is required');

// Login form validation schema
export const loginValidationSchema = yup.object({
  username: emailValidation.label('Email'),
  password: passwordValidation.label('Password'),
});
